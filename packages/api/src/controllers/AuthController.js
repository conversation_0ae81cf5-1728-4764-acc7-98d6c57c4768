const _ = require('lodash');
const UserService = require('../services/UserService');
const userService = new UserService();

class AuthController {
  /**
   * This method will verify the user after decoding jwt token present in header or query params
   * It is used as the validate function in hapi-jwt2-auth.
   * @param {Object} decoded  : decoded jwt token with payload
   * @param {*} request
   * @param {*} h
   */
  static async validateUserToken(decoded, request, h) {
    try {
      /* find routes to skip user validation and return true */
      const skipValidationForRoutes =
        process.env.SKIP_USER_VALIDATION_ON_ROUTES.split(',');
      if (skipValidationForRoutes.includes(request.path)) {
        return { isValid: true };
      }

      /* Get user profile data from curl request from activate-im server */
      const user = await userService.getUserProfile(
        request.headers.authorization,
        decoded.email,
      );

      if (_.isEmpty(user)) {
        global.logger().info('validateUserToken: user info');
        global.logger().info(user);
        return {
          isValid: false,
          response: h
            .response({
              error: 'not_found',
              message: 'User does not exist',
              statusCode: 404,
            })
            .code(404),
        };
      } else {
        request.user = user;
        return { isValid: true };
      }
    } catch (error) {
      request.logger.error('Error in AuthController.validateUserToken', error);
      return {
        isValid: false,
        response: h
          .response({
            error: `something_went_wrong`,
            message: `Internal server error: ${error}`,
            statusCode: 500,
          })
          .code(500),
      };
    }
  }
}

module.exports = AuthController;
