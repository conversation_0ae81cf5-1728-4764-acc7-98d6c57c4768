const { errorHandler } = require('../../../utils/errorHandler');
const CustomerIo = require('../../../../config/customerio');

class CustomerIoService {
  /**
   * This method responsible for update user in customer io.
   */
  async create(id, payload) {
    try {
      const cio = await CustomerIo.getInstance();
      return await cio.identify(id, payload);
    } catch (error) {
      return errorHandler(error);
    }
  }
}

module.exports = new CustomerIoService();
