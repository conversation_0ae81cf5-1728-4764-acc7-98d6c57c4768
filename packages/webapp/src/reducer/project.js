import axios from 'axios';
import Router from 'next/router';
import { trim } from 'lodash';
import { reset } from 'redux-form';
import { get } from 'lodash';
import { returnSectionNames, pushToDataLayer } from 'lib/commonUtil';
import { getUserData } from './user';
import { updateUserMeta } from 'reducer/auth';
import { toast } from 'react-toastify';

/**
 * Gets the user's project data
 *
 * @type {object} status
 */
export function setVisibleSections(data) {
  return {
    type: 'VISIBLE_SECTIONS',
    payload: data,
  };
}

/**
 * Function to set interests
 *
 * @type {Array} status
 */
export function setCreatorTags(tags) {
  return {
    type: 'SET_CREATOR_TAGS',
    payload: tags,
  };
}

/**
 * set other docs data
 *
 * @type {object} status
 */
export function setDocsData(data) {
  return {
    type: 'SET_OTHER_DOCS_DATA',
    payload: data,
  };
}

/**
 * Gets the user's project data
 *
 * @type {object} status
 */
export function setSelectedIndex(data) {
  return {
    type: 'SET_SELECTED_INDEX',
    payload: data,
  };
}

/**
 * Set cover image url.
 *
 * @type {string} url
 */
export function setCoverImage(url) {
  return {
    type: 'SET_COVER_IMG',
    payload: url,
  };
}

/**
 * Set cover img upload status.
 *
 * @type {boolean} status
 */
export function setCoverImageUploadStatus(status) {
  return {
    type: 'SET_COVER_IMG_UPLOAD_STATUS',
    payload: status,
  };
}

/**
 * Set getty status
 *
 * @type {object} gettyStatus
 */
export function setGettyStatus(gettyStatus) {
  return {
    type: 'SET_GETTY_STATUS',
    payload: gettyStatus,
  };
}

/**
 * Set getty button status
 *
 * @type {object} gettyButtonStatus
 */
export function setGettyButtonStatus(gettyButtonStatus) {
  return {
    type: 'GETTY_BUTTON_STATUS',
    payload: gettyButtonStatus,
  };
}

/**
 * Gets the user's project data
 *
 * @type {object} status
 */
export function setTheme(data) {
  return {
    type: 'SET_THEME',
    payload: data,
  };
}

/**
 * Gets the user's project data
 *
 * @type {object} status
 */
export function setProjectPreviewData(data) {
  return {
    type: 'SET_PROJECT_DATA',
    payload: data,
  };
}

/**
 * Gets the user's project snapshot data
 *
 * @type {object} status
 */
export function setProjectSnapshotData(data) {
  return {
    type: 'SET_PROJECT_SNAPSHOT_DATA',
    payload: data,
  };
}

/**
 * Gets the user's project share data
 *
 * @type {object} status
 */
export function setProjectShareData(data) {
  return {
    type: 'SET_SHARE_DATA',
    payload: data,
  };
}

/**
 * Gets the user's project share data
 *
 * @type {object} status
 */
export function setSidebarStatus(data) {
  return {
    type: 'SET_SIDEBAR_STATUS',
    payload: data,
  };
}

/**
 * Set the clicked item of the sidebar data
 *
 * @type {object} status
 */
export function setClickedItemOfSidebar(data) {
  return {
    type: 'SET_CLICKED_ITEM_SIDEBAR',
    payload: data,
  };
}

/**
 *set the filtered item of the discovery data
 *
 * @type {object} status
 */
export function setFilterDiscoveryData(data) {
  return {
    type: 'SET_FILTER_DISCOVERY_DATA',
    payload: data,
  };
}

/**
 *set the filtered item of the discovery data
 *
 * @type {object} status
 */
export function filteredByDiscoveryInfo(data) {
  return {
    type: 'SET_FILTERED_INFO_DATA',
    payload: data,
  };
}

/**
 * Gets the user's project share history data
 *
 * @type {Array} data
 */
export function getHistoryList(data = []) {
  const publishSnapOnTop = [];
  for (let item = 0; item < data.length; item++) {
    if (data[item].publish) {
      publishSnapOnTop.splice(0, 0, data[item]);
    } else {
      publishSnapOnTop.push(data[item]);
    }
  }
  return {
    type: 'GET_SHARE_HISTORY_DATA',
    payload: publishSnapOnTop,
  };
}

/**
 * Set snap screen info modal status
 *
 * @type {boolean} status
 */
export function setSnapInfoModalStatus(data = []) {
  let status = false;
  if (data.length > 0) {
    status = false;
  } else {
    status = true;
  }
  return {
    type: 'SET_SNAP_MODAL_STATUS',
    payload: status,
  };
}

/**
 * Gets the user's project data
 *
 * @type {object} status
 */
export function setImageData(data) {
  return {
    type: 'SET_IMAGE_DATA',
    payload: data,
  };
}
/**
 * Show save button on project cover
 *
 * @type {boolean} status
 */
export function setSaveButtonState(showButton) {
  return {
    type: 'SHOW_SAVE_BUTTON',
    payload: showButton,
  };
}

/**
 * Show save button on project cover
 *
 * @type {boolean} status
 */
export function setEditSection(editSection) {
  return {
    type: 'SET_EDIT_SECTION',
    payload: editSection,
  };
}

/**
 * Show save button on project cover
 *
 * @type {boolean} status
 */
export function setIsClickedOnMenu(value) {
  return {
    type: 'SET_IS_CLICKED_ON_MENU',
    payload: value,
  };
}

/**
 * Show save button on project basic info
 *
 * @type {boolean} status
 */
export function setSaveButtonStateBasicInfo(showButtonBasic) {
  return {
    type: 'SHOW_SAVE_BUTTON_BASIC',
    payload: showButtonBasic,
  };
}

/**
 * @type {*} paginationParams
 */
export function setPaginationParams(
  paginationParams = {
    $skip: 0,
    $limit: 5,
  },
) {
  return {
    type: 'SET_PAGINATION_PARAMS',
    payload: paginationParams,
  };
}

/**
 * Show save button on project artWorl
 *
 * @type {boolean} status
 */
export function setSaveButtonStateArtWork(showButtonArtwork) {
  return {
    type: 'SHOW_SAVE_ARTWORK',
    payload: showButtonArtwork,
  };
}
/**
 * Show save button on project basic info
 *
 * @type {boolean} status
 */
export function setSaveButtonStateDescription(showButtonDesc) {
  return {
    type: 'SHOW_SAVE_BUTTON_DESC',
    payload: showButtonDesc,
  };
}

/**
 * Gets the cover project data
 *
 * @type {object} status
 */
export function setCoverData(data) {
  return {
    type: 'GET_COVER_DATA',
    payload: data,
  };
}
/**
 * Gets the directorList project data
 *
 * @type {object} status
 */
export function setDirectorList(data) {
  return {
    type: 'SET_DIRECTOR_LIST',
    payload: data,
  };
}

/**
 * Gets the writerList project data
 *
 * @type {object} status
 */
export function setWriterList(data) {
  return {
    type: 'SET_WRITER_LIST',
    payload: data,
  };
}

/**
 * Gets the producerList project data
 *
 * @type {object} data
 */
export function setProducerList(data) {
  return {
    type: 'SET_PRODUCER_LIST',
    payload: data,
  };
}

/**
 * Gets the  project list
 *
 * @type {object} status
 */
export function setProjectList(projects) {
  return {
    type: 'GET_PROJECT_LIST',
    payload: projects,
  };
}

/**
 * Gets the message coming from api
 *
 * @type {string} status
 */
export function setMessage(msg) {
  return {
    type: 'SET_MESSAGE',
    payload: msg,
  };
}

/**
 * Function to enable loader
 *
 * @type {boolean} status
 */
export function setLoadingStatus(boolVal) {
  return {
    type: 'SET_LOADING',
    payload: boolVal,
  };
}
/**
 * Function to wait for receiving share data
 *
 * @type {boolean} status
 */
export function setDataStatus(boolVal) {
  return {
    type: 'SET_DATA_RECIEVE_STATUS',
    payload: boolVal,
  };
}

/**
 * Function to disable button
 *
 * @type {boolean} status
 */
export function setButtonStatus(boolVal) {
  return {
    type: 'DISABLE_BUTTON',
    payload: boolVal,
  };
}

/**
 * Function to set projects count
 *
 * @type {number} count
 */
export function setProjectCount(count) {
  return {
    type: 'SET_PROJECT_COUNT',
    payload: count,
  };
}

/**
 * Function to set video form open status
 *
 * @type {boolean} status
 */
export function setVideoFormStatus(status) {
  return {
    type: 'CLOSE_VIDEO_FORM',
    payload: status,
  };
}

/**
 * Function to set modal open status
 *
 * @type {boolean} status
 */
export function setModalShowStatus(status) {
  return {
    type: 'IS_MODLE_SHOW',
    payload: status,
  };
}

/**
 * This action called for set sales new snap request status.
 *
 * @type {boolean} status
 */
export function setSalesNewSnapStatus(status) {
  return {
    type: 'SET_SALES_REQUEST_STATUS',
    payload: status,
  };
}

/**
 * Set collaborator accepting status .
 *
 * @type {boolean}
 */
export function setCollaboratorAcceptingStatus(status) {
  return {
    type: 'SET_COLLABORATOR_ACCEPT',
    payload: status,
  };
}

/**
 * Function to set collaborator data.
 *
 * @type {object} data
 */
export function setCollaborator(data) {
  return {
    type: 'SET_COLLABORATOR_DETAILS',
    payload: data,
  };
}

/**
 * Function to set collaborator data.
 *
 * @type {object} data
 */
export function setCollaboratorList(data) {
  return {
    type: 'SET_COLLABORATOR_LIST',
    payload: data,
  };
}

/**
 * Function to set collaborator data.
 *
 * @type {object} data
 */
export function setMoodBoardImage(data) {
  return {
    type: 'SET_MOOD_IMAGE_OPEN',
    payload: data,
  };
}

/**
 * This function will call when need to change video form status.
 *
 * @returns {*} window url
 * @param {*} status form status
 */
export const isFormStatus = (status) => {
  return (dispatch) => {
    dispatch(setVideoFormStatus(status));
  };
};

/**
 * This function will call when need to change modal status.
 *
 * @param {*} status budget
 * @returns {*} window url
 */
export const isBudgetOverflow = (status = false) => {
  return (dispatch) => {
    dispatch(setModalShowStatus(status));
  };
};

/**
 * set snap request status.
 *
 * @type {object} status
 */
export function setSnapRequest(status) {
  return {
    type: 'SET_SNAP_REQUEST',
    payload: status,
  };
}

/**
 * set snap steps status.
 *
 * @type {object} status
 */
export function setSnapSteps(status) {
  return {
    type: 'SET_SNAP_STEPS',
    payload: status,
  };
}

/**
 * set finance edit item id.
 *
 * @type {string} id
 */
export function setFinanceEditId(id) {
  return {
    type: 'SET_FINANCE_EDIT_ID',
    payload: id,
  };
}

/**
 * set cover image url.
 *
 * @type {string} id
 */
export function setCoverImageUrl(url) {
  return {
    type: 'SET_COVER_IMAGE_URL',
    payload: url,
  };
}

/**
 * Set snap feedback
 *
 * @type {string} feedback
 */
export function snapFeedback(feedback) {
  return {
    type: 'SET_SNAP_FEEDBACK',
    payload: feedback,
  };
}

/**
 * Set discovery projects.
 *
 * @type {string} feedback
 */
export function setDiscovery(projects) {
  return {
    type: 'SET_DISCOVERY_PROJECTS',
    payload: projects,
  };
}

/**
 * Set DM projects.
 *
 * @type {string} feedback
 */
export function dmProjects(projects) {
  return {
    type: 'SET_DM_PROJECTS',
    payload: projects,
  };
}

/**
 * Set project notification list.
 *
 * @type {Array} notifications
 */
export function setProjectsNotification(notifications) {
  return {
    type: 'SET_NOTIFICATIONS',
    payload: notifications,
  };
}

/**
 * Set left side bar active tab.
 *
 * @type {string} tab
 */
export function setLeftSideBarActiveTab(tab) {
  return {
    type: 'SET_LEFT_SIDE_BAR_TAB',
    payload: tab,
  };
}

/**
 * Set left side bar active tab.
 *
 * @type {string} tab
 */
export function setCollapseSideBarActiveTab(tab) {
  return {
    type: 'SET_COLLAPSE_LEFT_SIDE_BAR_TAB',
    payload: tab,
  };
}

/**
 * Set project notification list.
 *
 * @type {Array} notifications
 */
export function setProjectsNotificationCount(count) {
  return {
    type: 'SET_NOTIFICATIONS_COUNT',
    payload: count,
  };
}

/**
 * Set project active goals in left side bar.
 *
 * @type {object} goals
 */
export function setActiveGoals(goals) {
  return {
    type: 'ACTIVE_GOAL',
    payload: goals,
  };
}

export function setAllProjectCount(count) {
  return {
    type: 'ALL_PROJECT_COUNT',
    payload: count,
  };
}

/**
 * Get the collaborator modal info modal status.
 *
 * @type {boolean} status
 */
export function setCollaboratorInfoModalStatus(status) {
  return {
    type: 'SET_COLLABORATOR_INFO_MODAL_STATUS',
    payload: status,
  };
}

/**
 * This method call when close generate snap modal.
 *
 * @returns {*} object
 * @param note
 */
export const closeSnapStepsModal = () => {
  return (dispatch) => {
    dispatch(
      setSnapSteps({
        getStarted: false,
        addNote: false,
        generateQr: false,
      }),
    );
    dispatch(setSalesNewSnapStatus(false));
  };
};

/**
 * method to generate qr code on 3nd step of share project
 *
 * @returns {*} object
 * @param note
 */
export const isGenerateQr = (note) => {
  return (dispatch, getState) => {
    const { projectPreviewData } = getState().project;

    dispatch(
      setSnapSteps({
        addNote: false,
        getStarted: false,
        generateQr: true,
      }),
    );
    const formData = { notes: '' };
    if (note.note !== undefined) formData.notes = note.note;

    dispatch(shareProject(projectPreviewData._id, formData));
  };
};

/**
 * Get all the project list that have been created by the user.
 *
 * @param {object} data project List
 * @returns {*} object
 */
export const getProjectList = (data) => (dispatch, getState) => {
  const params = data.params || {};
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/list?$limit=${process.env.ListApiLimit}&$sort=updatedAt|-1`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token || data.id_token}`,
    },
    method: 'get',
    params,
    responseType: 'json',
  })
    .then((response) => {
      const projects = get(response, 'data.data.docs', []);
      dispatch(setProjectList(projects));
      dispatch(setProjectCount(get(response, 'data.data.total', 0)));
      return projects;
    })
    .catch((error) => {
      if (get(error, 'response.status', '') === 404) {
        dispatch(setProjectList([]));
        dispatch(setProjectCount(0));
      }
      return [];
    });
};

export const getAllProjectList = () => (dispatch, getState) => {
  const params = {};
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/list?$limit=${process.env.ListApiLimit}&$sort=updatedAt|-1`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'get',
    params,
    responseType: 'json',
  })
    .then((response) => {
      const projects = get(response, 'data.data.docs', []);
      dispatch(setAllProjectCount(get(response, 'data.data.total', 0)));
      return projects;
    })
    .catch((error) => {
      if (get(error, 'response.status', '') === 404) {
        dispatch(setAllProjectCount(0));
      }
      return [];
    });
};

/**
 * Toggle theme
 *
 * @param {*} value - theme value
 * @returns
 */
export const toggleTheme = (value) => {
  return (dispatch) => {
    const themeClass = document.getElementsByClassName('themeContainer');
    if (themeClass && themeClass.length > 0) {
      dispatch(setTheme(value));
      themeClass[0].setAttribute('data-theme', value);
    }
  };
};

/**
 * Toggle onboarding theme
 *
 * @param {*} value - theme value
 */
export const toggleOnboarding = (value) => {
  const themeClass = document.getElementsByClassName('themeContainer');
  if (themeClass && themeClass.length > 0) {
    themeClass[0].setAttribute('data-theme', value);
  }
};

/**
 * get history project list.
 *
 * @param {object} id project id
 * @returns {*} object
 *
 */
export const getHistory = (id) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/snapshot/list?projectId=${id}&$limit=${process.env.ListApiLimit}&$sort=updatedAt|-1`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'get',
    responseType: 'json',
  })
    .then((response) => {
      dispatch(getHistoryList(response.data.data.docs));
      dispatch(setSnapInfoModalStatus(response.data.data.docs));
    })
    .catch(() => {});
};

/**
 * get project snapshot list.
 *
 * @param {object} id project id
 * @returns {*} object
 *
 */
export const getProjectSnapshots = (query) => (dispatch, getState) => {
  const params = {
    ...query,
    $limit: process.env.ListApiLimit,
    $sort: 'updatedAt|-1',
  };
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/snapshot/list`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    params,
    method: 'get',
    responseType: 'json',
  })
    .then((response) => {
      dispatch(getHistoryList(response.data.data.docs));
      dispatch(setSnapInfoModalStatus(response.data.data.docs));
    })
    .catch(() => {});
};

/**
 * Soft delete specific project.
 *
 * @param {object} id project List
 * @returns {*} object
 *
 */
export const softDeleteProject = (id) => (dispatch, getState) => {
  dispatch(setLoadingStatus(true));
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/soft/${id}`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'delete',
    responseType: 'json',
  })
    .then(() => {
      const projects = getState().project.projectsList;
      dispatch(setLoadingStatus(false));
      dispatch(setProjectList(projects.filter((item) => item._id !== id)));
      pushToDataLayer({
        action: 'Project soft deleted',
        category: 'Project',
        label: 'Project soft deletion success',
      });
    })
    .catch(() => {
      dispatch(setLoadingStatus(false));
    });
};

/**
 * Delete Specific project.
 *
 * @param {object} id project List
 * @returns {*} object
 *
 */
export const deleteProject = (id) => (dispatch, getState) => {
  dispatch(setLoadingStatus(true));
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/delete`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'delete',
    responseType: 'json',
  })
    .then(() => {
      dispatch(setLoadingStatus(false));
      pushToDataLayer({
        action: 'Project deleted',
        category: 'Project',
        label: 'Project deletion success',
      });
      const projects = getState().project.projectsList;
      dispatch(setProjectList(projects.filter((item) => item._id !== id)));
    })
    .catch(() => {
      dispatch(setLoadingStatus(false));
    });
};

/**
 * Api for creating project
 *
 * @type {object} status
 */
export const createProject = (data) => (dispatch, getState) => {
  const value = {
    title: trim(data.title),
    producer: data.producer,
    director: data.director,
    writer: data.writer,
    genre: data.genre,
  };

  dispatch(setLoadingStatus(true));
  dispatch(setMessage(''));

  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/create`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'post',
    data: value,
    responseType: 'json',
  })
    .then((response) => {
      const projectId = response.data.data._id;
      const data = {
        userMeta: {
          redirectUrl: `/dashboard`,
        },
      };
      dispatch(updateUserMeta(data, true));
      dispatch(setProjectPreviewData(response.data.data));
      pushToDataLayer({
        action: 'Project created',
        category: 'Project',
        label: 'Project created succcess',
      });
      // Router.push(`/project/preview/${projectId}`);
      Router.push(`/project/synopsis/${projectId}`);
      dispatch(setLoadingStatus(false));
    })
    .catch((err) => {
      if (get(err, 'response.status') === 409) {
        dispatch(setMessage(err.response.data.message));
      }
      dispatch(setLoadingStatus(false));
    });
};

/**
 * Api for sharing project
 *
 * @type {object} status
 */
export const shareProject = (id, data) => (dispatch, getState) => {
  dispatch(setDataStatus(true));
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/share`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'post',
    data: data,
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setProjectShareData(response.data.data));
      pushToDataLayer({
        action: 'snapshot created',
        category: 'Snapshot',
        label: 'User creates a project snapshot',
      });
      dispatch(getHistory(id));
      dispatch(fetchProjectDetailsByID(id, false));
      dispatch(setDataStatus(false));
    })
    .catch(() => {
      dispatch(setDataStatus(false));
      pushToDataLayer({
        action: 'snapshot error',
        category: 'Snapshot',
        label: 'Snapshot creation fails',
      });
    });
};

/**
 * Api for publish snap of project
 *
 * @type {object} status
 */
export const isPublishSnap = (id, projectId, data) => (dispatch, getState) => {
  dispatch(setDataStatus(true));
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/snap/${id}/publish`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'patch',
    data: data,
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setProjectShareData(response.data.data));
      dispatch(getHistory(projectId));
      dispatch(fetchProjectDetailsByID(projectId, false));
      return response;
    })
    .catch(() => {});
};

/**
 * Project details by id
 *
 * @param {*} projectId Project Id
 * @param {*} isShowLoader loader
 * @returns {*} object
 */
export const fetchProjectDetailsByID =
  (projectId, isShowLoader = true) =>
  (dispatch, getState) => {
    if (isShowLoader) {
      dispatch(setLoadingStatus(true));
    }
    return axios({
      url: `${process.env.SmashApiBaseUrl}/v1/project/${projectId}`,
      headers: {
        Authorization: `Bearer ${getState().auth.token}`,
      },
      method: 'get',
      responseType: 'json',
    })
      .then((response) => {
        const res = response.data.data;
        const goal = get(res, 'goals.goal', false);
        if (goal) {
          dispatch(setActiveGoals(goal));
        }
        dispatch(setProjectCount(1));
        dispatch(setProjectPreviewData(response.data.data));
        dispatch(setCoverData(response.data.data.cover));
        dispatch(setImageData(response.data.data.artWork));
        const producerList = [];
        const writerList = [];
        const directorList = [];
        if (
          response.data.data !== null &&
          response.data.data !== undefined &&
          response.data.data.creativeTeam.length > 0
        ) {
          response.data.data.creativeTeam.map((item) => {
            if (
              item.jobRole === 'producer' ||
              item.jobRole === 'Producer' ||
              item.jobRole === 'Director/Producer'
            ) {
              producerList.push(item.name);
            }
            if (
              item.jobRole === 'writer' ||
              item.jobRole === 'Writer' ||
              item.jobRole === 'Writer/Director'
            ) {
              writerList.push(item.name);
            }
            if (
              item.jobRole === 'director' ||
              item.jobRole === 'Director' ||
              item.jobRole === 'Writer/Director' ||
              item.jobRole === 'Director/Producer'
            ) {
              directorList.push(item.name);
            }
          });
        }
        dispatch(setProducerList(producerList));
        dispatch(setDirectorList(directorList));
        dispatch(setWriterList(writerList));
        dispatch(setLoadingStatus(false));
        dispatch(toggleTheme(response.data.data.theme));
        dispatch(
          setProjectsNotificationCount(
            get(response, 'data.data.projectMeta.isNewNotification', false),
          ),
        );
        return response.data.data;
      })
      .catch(() => {
        dispatch(setLoadingStatus(false));
      });
  };

/**
 * Project details by hash
 *
 * @param {*} hashId hash Id
 * @returns {*} object
 */
export const fetchProjectDetailsByHashID = (hashId) => (dispatch, getState) => {
  dispatch(setLoadingStatus(true));
  return new Promise((resolve) => {
    axios({
      url: `${process.env.SmashApiBaseUrl}/v1/project/snapshot/list?hash=${hashId}`,
      headers: {
        Authorization: `Bearer ${getState().auth.token}`,
      },
      method: 'get',
      responseType: 'json',
    })
      .then((response) => {
        const themeData = JSON.parse(response.data.data.docs[0].body);
        dispatch(setProjectSnapshotData(response.data.data.docs[0]));
        dispatch(setLoadingStatus(false));
        dispatch(toggleTheme(themeData.theme));
        resolve(response.data.data.docs[0]);
      })
      .catch(() => {
        dispatch(setLoadingStatus(false));
        resolve({});
      });
  });
};

/**
 * Update details of project
 *
 * @param {*} value value
 * @param {*} id Project Id
 * @returns {*} void
 */
export const updateProjectData = (value, id) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/update`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'patch',
    data: value,
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setProjectPreviewData(response.data.data));
      if ('deleted' in value) {
        const projects = getState().project.projectsList;
        dispatch(setProjectList(projects.filter((item) => item._id !== id)));
      }
    })
    .catch(() => {});
};

/**
 * Delete file with the help o fid and section
 *
 * @param {*} id project id
 * @param {*} section section name
 * @returns {*} deleted file
 */
export const deleteFile = (id, section) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/file/${id}/${section}`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'delete',
    responseType: 'json',
  })
    .then((response) => {
      if (section === 'coverPic') {
        dispatch(setProjectPreviewData(response.data.data));
      }
    })
    .catch(() => {});
};

/**
 * Update details of project cover
 *
 * @param {*} value value
 * @param {*} id Project Id
 * @param action
 * @returns {*} void
 */
export const updateCoverInfo = (value, id, action) => (dispatch, getState) => {
  dispatch(setButtonStatus(true));
  const modifiedValue = {
    ...value,
    title: trim(value.title),
  };
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/cover`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'patch',
    data: modifiedValue,
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setProjectPreviewData(response.data.data));
      dispatch(setCoverData(response.data.data.cover));
      if (action === 'close') {
        dispatch(setButtonStatus(false));
        dispatch(setSaveButtonState(false));
        dispatch(setEditSection(''));
      }
    })
    .catch(() => {});
};

/**
 * Update details of project basic knf
 *
 * @param {*} value value
 * @param {*} id Project Id
 * @returns {*} void
 */
export const updateProjectBasicInfo = (value, id) => (dispatch, getState) => {
  dispatch(setButtonStatus(true));
  if (value.logLine) {
    value.logLine = value.logLine.trim();
  }
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/basicInfo`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'patch',
    data: value,
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setProjectPreviewData(response.data.data));
      dispatch(setButtonStatus(false));
      dispatch(setSaveButtonStateBasicInfo(false));
      dispatch(setEditSection(''));
    })
    .catch(() => {});
};

/**
 * Update creative team.
 *
 * @param {*} id Project Id
 *  @type {object} value
 */
export const updateCreativeTeam =
  (value, id, redirectPage) => (dispatch, getState) => {
    dispatch(setButtonStatus(true));
    return axios({
      url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/creativeTeam`,
      headers: {
        Authorization: `Bearer ${getState().auth.token}`,
      },
      method: 'patch',
      data: value,
      responseType: 'json',
    })
      .then((response) => {
        const producerList = [];
        const writerList = [];
        const directorList = [];
        if (
          response.data.data !== null &&
          response.data.data !== undefined &&
          response.data.data.creativeTeam.length > 0
        ) {
          response.data.data.creativeTeam.map((item) => {
            if (
              item.jobRole === 'producer' ||
              item.jobRole === 'Producer' ||
              item.jobRole === 'Director/Producer' ||
              item.jobRole === 'Writer/Producer'
            ) {
              producerList.push(item.name);
            }
            if (
              item.jobRole === 'writer' ||
              item.jobRole === 'Writer' ||
              item.jobRole === 'Writer/Director' ||
              item.jobRole === 'Writer/Producer'
            ) {
              writerList.push(item.name);
            }
            if (
              item.jobRole === 'director' ||
              item.jobRole === 'Director' ||
              item.jobRole === 'Writer/Director' ||
              item.jobRole === 'Director/Producer'
            ) {
              directorList.push(item.name);
            }
          });
        }
        dispatch(setProjectPreviewData(response.data.data));
        dispatch(setButtonStatus(false));
        dispatch(setEditSection(''));
        dispatch(setProducerList(producerList));
        dispatch(setDirectorList(directorList));
        dispatch(setWriterList(writerList));
        dispatch(redirectPage());
      })
      .catch(() => {
        dispatch(setLoadingStatus(false));
      });
  };

/**
 * Update cast members.
 *
 * @param {*} id Project Id
 *  @type {object} value
 */
export const updateCrewMembers =
  (value, id, redirectPage) => (dispatch, getState) => {
    dispatch(setButtonStatus(true));
    return axios({
      url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/castMembers`,
      headers: {
        Authorization: `Bearer ${getState().auth.token}`,
      },
      method: 'patch',
      data: value,
      responseType: 'json',
    })
      .then((response) => {
        dispatch(setProjectPreviewData(response.data.data));
        dispatch(setButtonStatus(false));
        dispatch(setEditSection(''));
        dispatch(redirectPage());
      })
      .catch(() => {
        dispatch(setLoadingStatus(false));
      });
  };

/**
 * Create and update Finance and budget.
 *
 * @param {*} id Project Id
 * @type {object} value
 * @param {Function} redirectPage
 */
export const updateFinance = (value, id) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/financePlan`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'patch',
    data: value,
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setProjectPreviewData(response.data.data));
    })
    .catch(() => {
      dispatch(setLoadingStatus(false));
    });
};

/**
 * This method call for update budget
 * @param {*} value - budget value
 * @param {*} id - project id
 * @param {*} redirect - redirect page
 * @returns {*} object
 */
export const updateBudget = (value, id, redirect) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/budget`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'patch',
    data: value,
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setProjectPreviewData(response.data.data));
      dispatch(setButtonStatus(false));
      dispatch(setEditSection(''));
      dispatch(redirect());
    })
    .catch(() => {
      dispatch(setLoadingStatus(false));
    });
};

/**
 * Update comparable project.
 *
 * @param {*} id Project Id
 *  @type {object} value
 */
export const updateCompareProject =
  (value, id, redirectPage) => (dispatch, getState) => {
    return axios({
      url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/comparableProject`,
      headers: {
        Authorization: `Bearer ${getState().auth.token}`,
      },
      method: 'patch',
      data: value,
      responseType: 'json',
    })
      .then((response) => {
        dispatch(setProjectPreviewData(response.data.data));
        dispatch(redirectPage());
        dispatch(setEditSection(''));
      })
      .catch(() => {
        dispatch(setEditSection(''));
        dispatch(setLoadingStatus(false));
      });
  };

/**
 * Update project posters.
 *
 * @param {*} id Project Id
 *  @type {object} value
 */
export const updateProjectPosters =
  (value, id, redirectPage) => (dispatch, getState) => {
    return axios({
      url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/filmsPoster`,
      headers: {
        Authorization: `Bearer ${getState().auth.token}`,
      },
      method: 'patch',
      data: value,
      responseType: 'json',
    })
      .then((response) => {
        dispatch(setProjectPreviewData(response.data.data));
        dispatch(redirectPage());
        dispatch(setEditSection(null));
      })
      .catch(() => {
        dispatch(setEditSection(''));
      });
  };

/**
 * Update Description and vision of project.
 *
 * @param {*} id Project Id
 *  @type {object} value
 */
export const updateDescription =
  (value, id, showForm) => (dispatch, getState) => {
    dispatch(setButtonStatus(true));
    return axios({
      url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/projectDisc`,
      headers: {
        Authorization: `Bearer ${getState().auth.token}`,
      },
      method: 'patch',
      data: value,
      responseType: 'json',
    })
      .then((response) => {
        dispatch(setProjectPreviewData(response.data.data));
        dispatch(setButtonStatus(false));
        dispatch(setEditSection(''));
        dispatch(setSaveButtonStateDescription(false));
        dispatch(showForm());
      })
      .catch(() => {
        dispatch(setEditSection(''));
        dispatch(setSaveButtonStateDescription(false));
        dispatch(setLoadingStatus(false));
      });
  };

/**
 * Update Project Artwork  of project.
 *
 * @param {*} id Project Id
 *  @type {object} value
 */
export const updateProjectArtwork =
  (action, value, id, type, successMsg) => (dispatch, getState) => {
    if (action === 'close') {
      dispatch(setEditSection(''));
      dispatch(setSaveButtonStateArtWork(false));
    } else {
      dispatch(setButtonStatus(true));
      return axios({
        url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/${type}`,
        headers: {
          Authorization: `Bearer ${getState().auth.token}`,
        },
        method: 'patch',
        data: value,
        responseType: 'json',
      })
        .then((response) => {
          dispatch(setProjectPreviewData(response.data.data));
          dispatch(setImageData(response.data.data.artWork));
          dispatch(setButtonStatus(false));
          toast.success(successMsg);
        })
        .catch(() => {
          dispatch(setLoadingStatus(false));
        });
    }
  };

/**
 * Update Videos .
 *
 * @param {*} id Project Id
 *  @type {object} value
 */
export const updateVideos = (value, id, isAddVideo) => (dispatch, getState) => {
  dispatch(setButtonStatus(true));
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/videos`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'patch',
    data: value,
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setProjectPreviewData(response.data.data));
      dispatch(setButtonStatus(false));
      dispatch(setEditSection(''));
      const sections = returnSectionNames();
      dispatch(setVisibleSections(sections));
      dispatch(setIsClickedOnMenu(false));
      dispatch(reset('videoForm'));
      dispatch(setVideoFormStatus(false));
      dispatch(isAddVideo());
    })
    .catch(() => {
      dispatch(setLoadingStatus(false));
    });
};

/**
 * This method is used to remove section items.
 *
 * @param {*} projectId - project id
 * @param {*} section - section name
 * @param {*} sectionId - section id
 * @returns
 */
export const removeSectionItems =
  (projectId, section, sectionId) => (dispatch, getState) => {
    dispatch(setButtonStatus(true));
    return axios({
      url: `${process.env.SmashApiBaseUrl}/v1/project/${projectId}/remove/${section}/${sectionId}`,
      headers: {
        Authorization: `Bearer ${getState().auth.token}`,
      },
      method: 'delete',
      responseType: 'json',
    })
      .then((response) => {
        dispatch(setProjectPreviewData(response.data.data));
        dispatch(setImageData(response.data.data.artWork));
        dispatch(setDocsData(response.data.data.otherDocs));
        dispatch(reset('videoForm'));
        dispatch(setVideoFormStatus(false));
        dispatch(setButtonStatus(false));
        if (
          section !== 'artWork' &&
          section !== 'videos' &&
          section !== 'salesEstimateFile' &&
          section !== 'otherDocs' &&
          section !== 'financePlan'
        ) {
          dispatch(setEditSection(''));
        }
      })
      .catch(() => {
        dispatch(setLoadingStatus(false));
      });
  };

/**
 * Update Sales Estimate of project.
 *
 * @param {*} id Project Id
 * @type {object} value
 */
export const updateSalesEstimation =
  (value, id, section) => (dispatch, getState) => {
    dispatch(setButtonStatus(true));
    return axios({
      url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/${section}`,
      headers: {
        Authorization: `Bearer ${getState().auth.token}`,
      },
      method: 'patch',
      data: value,
      responseType: 'json',
    })
      .then((response) => {
        dispatch(setProjectPreviewData(response.data.data));
        dispatch(setButtonStatus(false));
      })
      .catch(() => {
        dispatch(setLoadingStatus(false));
      });
  };

/**
 * Update Other documents of project.
 *
 * @param {*} id Project Id
 *  @type {object} value
 */
export const updateOtherDocuments =
  (value, id, section) => (dispatch, getState) => {
    dispatch(setButtonStatus(true));
    return axios({
      url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/${section}`,
      headers: {
        Authorization: `Bearer ${getState().auth.token}`,
      },
      method: 'patch',
      data: value,
      responseType: 'json',
    })
      .then((response) => {
        dispatch(setProjectPreviewData(response.data.data));
        dispatch(setDocsData(response.data.data.otherDocs));
        dispatch(setButtonStatus(false));
      })
      .catch(() => {
        dispatch(setLoadingStatus(false));
      });
  };

/**
 * Api for getty sign in.
 *
 * @type {object} status
 */
export const createGettySignIn = (values) => (dispatch, getState) => {
  dispatch(setButtonStatus(true));
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/getty/signup`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'post',
    data: values,
    responseType: 'json',
  })
    .then(() => {
      dispatch(setButtonStatus(false));
      dispatch(setGettyStatus(true));
      dispatch(setGettyButtonStatus(true));
      dispatch(getUserData());
      pushToDataLayer({
        action: 'getty submit signup success',
        category: 'Project moodboard',
        label: 'Getty signup success',
      });
    })
    .catch(() => {
      dispatch(setButtonStatus(true));
      dispatch(setGettyStatus(false));
      dispatch(setGettyButtonStatus(false));
    });
};

export const deleteDescriptionFile =
  (projectId, fileType) => (dispatch, getState) => {
    dispatch(setButtonStatus(true));
    return axios({
      url: `${process.env.SmashApiBaseUrl}/v1/project/${projectId}/description/${fileType}`,
      headers: {
        Authorization: `Bearer ${getState().auth.token}`,
      },
      method: 'delete',
      responseType: 'json',
    })
      .then((response) => {
        dispatch(setProjectPreviewData(response.data.data));
      })
      .catch(() => {});
  };

/**
 * Api for creating or update feedback of snap.
 *
 * @type {object} data
 */
export const createFeedback = (data) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/snap/action`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'patch',
    data: data,
    responseType: 'json',
  })
    .then((response) => {
      dispatch(snapFeedback(response.data.data));
    })
    .catch(() => {});
};

// /**
//  * Get Project snap feedback
//  *
//  * @param {object} query
//  * @returns {*} string
//  */
// export const fetchFeedbackOfSnap = (query) => (dispatch, getState) => {
//   return axios({
//     url: `${process.env.SmashApiBaseUrl}/v1/project/feedback?snapshotId=${query.snapshotId}&decisionMakerInfo.decisionMakerId=${query.decisionMakerInfo.decisionMakerId}`,
//     headers: {
//       Authorization: `Bearer ${getState().auth.token}`,
//     },
//     method: 'get',
//     responseType: 'json',
//   })
//     .then((response) => {
//       dispatch(snapFeedback(response.data.data.docs[0].feedback));
//     })
//     .catch((error) => {
//       if (error.response && error.response.status === 404) {
//         dispatch(trackSnap(query));
//       }
//     });
// };

// /**
//  * Get Project snap feedback
//  *
//  * @param {object} query
//  * @returns {*} string
//  */
// export const fetchFeedbackOfProject = () => (dispatch, getState) => {
//   dispatch(setProjectsNotification(null));
//   return axios({
//     url: `${process.env.SmashApiBaseUrl}/v1/snap`,
//     headers: {
//       Authorization: `Bearer ${getState().auth.token}`,
//     },
//     method: 'get',
//     responseType: 'json',
//   })
//     .then((response) => {
//       dispatch(setProjectsNotification(response.data.data));
//     })
//     .catch(() => {});
// };

/**
 * Get Project snap feedback
 *
 * @param {object} query
 * @returns {*} string
 */
export const fetchDmProjects = () => (dispatch, getState) => {
  dispatch(setProjectsNotification(null));
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/snap`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'get',
    responseType: 'json',
  })
    .then((response) => {
      dispatch(dmProjects(response.data.data));
      dispatch(setProjectsNotification(response.data.data));
      return response.data.data;
    })
    .catch(() => {});
};

/**
 * Update project meta
 *
 * @param {*} value value
 * @param {*} id Project Id
 * @returns {*} void
 */
export const updateProjectMeta = (value, id) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/projectMeta`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'patch',
    data: value,
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setProjectPreviewData(response.data.data));
      dispatch(
        setProjectsNotificationCount(
          get(response, 'data.data.projectMeta.isNewNotification', false),
        ),
      );
    })
    .catch(() => {});
};

/**
 * Get collaborator list data.
 *
 * @param {object} query
 * @returns {*} string
 */
export const fetchCollaboratorList = (projectId) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/collaborator/list?$limit=1000000000&$sort=updatedAt|-1&projectInfo.projectsId=${projectId}`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'get',
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setCollaboratorList(response.data.data.docs));
      return response.data.data.docs;
    })
    .catch(() => {});
};

/**
 * Get collaborator data.
 *
 * @param {object} query
 * @returns {*} string
 */
export const fetchCollaborator = (query) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/collaborator/list?$limit=${process.env.ListApiLimit}&$sort=updatedAt|-1&email=${query.email}&projectInfo.projectsId=${query.id}`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'get',
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setCollaborator(response.data.data.docs));
      return response.data.data.docs;
    })
    .catch(() => {});
};

/**
 * Update collaborator details.
 *
 * @param {*} value value
 * @param {*} id Collaborator Id
 * @returns {*} Collaborator data
 */
export const updateCollaborator = (value, id) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/collaborator/${id}/update`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'patch',
    data: value,
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setCollaborator(response.data.data));
      dispatch(setCollaboratorAcceptingStatus(false));
    })
    .catch(() => {});
};

/**
 * Send reminder to collaborator and update details.
 *
 * @param {*} value value
 * @param {*} id Collaborator Id
 * @returns {*} Collaborator data
 */
export const sendReminderToCollaborator =
  (value, id) => (dispatch, getState) => {
    return axios({
      url: `${process.env.SmashApiBaseUrl}/v1/collaborator/${id}/reminder`,
      headers: {
        Authorization: `Bearer ${getState().auth.token}`,
      },
      method: 'patch',
      data: value,
      responseType: 'json',
    })
      .then((response) => {
        dispatch(setCollaborator(response.data.data));
      })
      .catch(() => {});
  };

/**
 * Create collaborator details.
 *
 * @param {*} value value
 * @param {*} id Collaborator Id
 * @returns {*} Collaborator data
 */
export const createCollaborator = (value) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/collaborator/create`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'post',
    data: value,
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setCollaborator(response.data.data));
      return response.data.data;
    })
    .catch(() => {});
};

/**
 * Delete Specific collaborator.
 *
 * @param {object} id string
 * @returns {*} object
 */
export const deleteCollaborator = (id) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/collaborator/${id}/delete`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'delete',
    responseType: 'json',
  })
    .then(() => {
      dispatch(setLoadingStatus(false));
    })
    .catch(() => {
      dispatch(setLoadingStatus(false));
    });
};

/**
 * Update section locking of project.
 *
 * @param {*} id Project id
 * @param {*} action lock/remove
 * @param {*} payload section name
 * @returns {*} project data
 */
export const updateSectionLocking =
  (id, action, value) => (dispatch, getState) => {
    dispatch(setLoadingStatus(false));
    return axios({
      url: `${process.env.SmashApiBaseUrl}/v1/project/sectionEdit/${id}/${action}`,
      headers: {
        Authorization: `Bearer ${getState().auth.token}`,
      },
      method: 'patch',
      data: value,
      responseType: 'json',
    })
      .then((response) => {
        dispatch(setProjectPreviewData(response.data.data));
      })
      .catch(() => {
        dispatch(setLoadingStatus(false));
      });
  };

/**
 * Update Sales Estimate of project.
 *
 * @param {*} id Project Id
 * @type {object} value
 */
export const updateUnestimatedBudget =
  (value, id, section) => (dispatch, getState) => {
    return axios({
      url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/${section}`,
      headers: {
        Authorization: `Bearer ${getState().auth.token}`,
      },
      method: 'patch',
      data: value,
      responseType: 'json',
    })
      .then((response) => {
        dispatch(setProjectPreviewData(response.data.data));
      })
      .catch(() => {});
  };

/**
 * Update goals of project.
 *
 * @param {*} id Project Id
 *  @type {object} value
 */
export const updateGoals = (value, id) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/goals`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'patch',
    data: value,
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setProjectPreviewData(response.data.data));
      dispatch(setActiveGoals(response.data.data.goals.goal));
    })
    .catch(() => {});
};

/**
 * Api for creating discovery project snap list.
 *
 * @type {object} data
 */
export const createDiscoveryProject = (data) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/discovery/create`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'post',
    data: data,
    responseType: 'json',
  })
    .then(() => {
      dispatch(setLoadingStatus(false));
    })
    .catch(() => {
      dispatch(setLoadingStatus(false));
    });
};

/**
 * Delete Specific discovery project.
 *
 * @param {object} id project id
 * @returns {*} object
 */
export const deleteDiscoveryProject = (id) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/discovery/${id}/delete`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'delete',
    responseType: 'json',
  })
    .then(() => {
      dispatch(setLoadingStatus(false));
    })
    .catch(() => {
      dispatch(setLoadingStatus(false));
    });
};

/**
 * get discovery project list.
 *
 * @param {object} id project id
 * @returns {*} object
 */
export const getDiscoveryProjects = () => (dispatch, getState) => {
  dispatch(setLoadingStatus(true));
  const selectField =
    'basicInfo.logLine basicInfo.format basicInfo.genre basicInfo.setting basicInfo.runningTime basicInfo.status basicInfo.tags otherDocs projectDisc.script.name';
  const select =
    'snapshotId createdAt deleted projectInfo projectOwnerInfo.name';
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/discovery/list?$limit=${process.env.ListApiLimit}&$sort=updatedAt|-1&$populate=snapshotId&selectField=${selectField}&$select=${select}`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'get',
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setLoadingStatus(false));
      dispatch(setDiscovery(response.data.data.docs));
    })
    .catch(() => {
      dispatch(setLoadingStatus(false));
    });
};

/**
 * Update Other documents of project.
 *
 * @param {*} id Project Id
 *  @type {object} value
 */

export const updateOnOffSections = (value, id) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/${id}/sectionsOnOff`,
    headers: {
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'patch',
    data: value,
    responseType: 'json',
  })
    .then((response) => {
      dispatch(setProjectPreviewData(response.data.data));
      dispatch(setButtonStatus(false));
      dispatch(setLoadingStatus(false));
    })
    .catch(() => {
      dispatch(setLoadingStatus(false));
    });
};

/**
 * Get all the call outs list that have been created by the user.
 *
 * @returns {*} object
 */
export const getSnapshot = (id) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/project/snapshot/${id}`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'get',
    responseType: 'json',
  })
    .then((response) => {
      const snapshot = get(response, 'data.data', {});
      return snapshot;
    })
    .catch(() => {
      return {};
    });
};

/* initialize state and Project reducer */
export const initialState = {
  isLoading: false,
  projectPreviewData: null,
  snapshotData: null,
  msg: '',
  projectsList: [],
  historyList: [],
  producerList: [],
  directorList: [],
  writerList: [],
  projectCount: 0,
  coverData: null,
  showButton: false,
  isDisable: false,
  showButtonBasic: false,
  showButtonDesc: false,
  showButtonArtwork: false,
  imageList: [],
  isOpenForm: false,
  shareData: null,
  loadData: false,
  paginationParams: {
    $skip: 0,
    $limit: 5,
  },
  isBudgetModalOpen: false,
  visibleSections: returnSectionNames(),
  theme: null,
  gettyStatus: false,
  gettyButtonStatus: false,
  editSection: '',
  selectedIndex: -1,
  docsList: [],
  isClickedOnMenu: false,
  tags: [],
  salesNewSnapRequest: false,
  snapStepsStatus: {
    addNote: false,
    getStarted: false,
    generateQr: false,
  },
  snapRequest: false,
  financeEditId: null,
  coverImageUrl: false,
  feedback: null,
  dmProjectsList: [],
  notificationsList: [],
  isNewNotification: false,
  // dmTrackSnap: null,
  collaboratorDetails: null,
  collaboratorAcceptStatus: false,
  collaboratorList: [],
  discoveryProjects: [],
  coverImg: null,
  coverImgUploadStatus: false,
  activeTab: 'project',
  collapseActiveTab: 'templates',
  collaboratorInfoModalStatus: false,
  leftSideActiveGoals: null,
  snapInfoModalStatus: false,
  isMoodImageOpen: false,
  isSidebarOpen: true,
  clickedItemSidebar: '',
  allProjectCount: 0,
  filteredData: {},
  isFilteredDiscoveryInfo: {},
};

/**
 * To set project object in redux
 *
 * @param {any} state - Redux state
 * @param {any} action - Redux action
 * @returns {any} This function return redux project state.
 */
const project = (state = initialState, action) => {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'SET_OTHER_DOCS_DATA':
      return {
        ...state,
        docsList: action.payload,
      };
    case 'SET_SELECTED_INDEX':
      return {
        ...state,
        selectedIndex: action.payload,
      };
    case 'SET_IMAGE_DATA':
      return {
        ...state,
        imageList: action.payload,
      };
    case 'DISABLE_BUTTON':
      return {
        ...state,
        isDisable: action.payload,
      };
    case 'SHOW_SAVE_BUTTON':
      return {
        ...state,
        showButton: action.payload,
      };
    case 'SHOW_SAVE_BUTTON_BASIC':
      return {
        ...state,
        showButtonBasic: action.payload,
      };
    case 'SHOW_SAVE_ARTWORK':
      return {
        ...state,
        showButtonArtwork: action.payload,
      };
    case 'SET_GETTY_STATUS':
      return {
        ...state,
        gettyStatus: action.payload,
      };
    case 'SHOW_SAVE_BUTTON_DESC':
      return {
        ...state,
        showButtonDesc: action.payload,
      };
    case 'SET_PRODUCER_LIST':
      return {
        ...state,
        producerList: action.payload,
      };
    case 'SET_DIRECTOR_LIST':
      return {
        ...state,
        directorList: action.payload,
      };
    case 'SET_WRITER_LIST':
      return {
        ...state,
        writerList: action.payload,
      };
    case 'SET_PAGINATION_PARAMS':
      return {
        ...state,
        paginationParams: action.payload,
      };
    case 'GET_COVER_DATA':
      return {
        ...state,
        coverData: action.payload,
      };
    case 'SET_PROJECT_DATA':
      return {
        ...state,
        projectPreviewData: action.payload,
      };
    case 'SET_MESSAGE':
      return {
        ...state,
        msg: action.payload,
      };
    case 'SET_FINANCE_EDIT_ID':
      return {
        ...state,
        financeEditId: action.payload,
      };
    case 'GET_PROJECT_LIST':
      return {
        ...state,
        projectsList: action.payload,
      };
    case 'SET_PROJECT_COUNT':
      return {
        ...state,
        projectCount: action.payload,
      };
    case 'CLOSE_VIDEO_FORM':
      return {
        ...state,
        isOpenForm: action.payload,
      };
    case 'SET_SHARE_DATA':
      return {
        ...state,
        shareData: action.payload,
      };
    case 'SET_DATA_RECIEVE_STATUS':
      return {
        ...state,
        loadData: action.payload,
      };
    case 'GETTY_BUTTON_STATUS':
      return {
        ...state,
        gettyButtonStatus: action.payload,
      };
    case 'SET_CREATOR_TAGS':
      return {
        ...state,
        tags: action.payload,
      };
    case 'GET_SHARE_HISTORY_DATA':
      return {
        ...state,
        historyList: action.payload,
      };
    case 'SET_PROJECT_SNAPSHOT_DATA':
      return {
        ...state,
        snapshotData: action.payload,
      };
    case 'IS_MODLE_SHOW':
      return {
        ...state,
        isBudgetModalOpen: action.payload,
      };
    case 'VISIBLE_SECTIONS':
      return {
        ...state,
        visibleSections: action.payload,
      };
    case 'SET_THEME':
      return {
        ...state,
        theme: action.payload,
      };
    case 'SET_EDIT_SECTION':
      return {
        ...state,
        editSection: action.payload,
      };
    case 'SET_IS_CLICKED_ON_MENU':
      return {
        ...state,
        isClickedOnMenu: action.payload,
      };
    case 'SET_SALES_REQUEST_STATUS':
      return {
        ...state,
        salesNewSnapRequest: action.payload,
      };
    case 'SET_SNAP_STEPS':
      return {
        ...state,
        snapStepsStatus: action.payload,
      };
    case 'SET_SNAP_REQUEST':
      return {
        ...state,
        snapRequest: action.payload,
      };
    case 'SET_COVER_IMAGE_URL':
      return {
        ...state,
        coverImageUrl: action.payload,
      };
    case 'SET_SNAP_FEEDBACK':
      return {
        ...state,
        feedback: action.payload,
      };
    case 'SET_DM_PROJECTS':
      return {
        ...state,
        dmProjectsList: action.payload,
      };
    case 'SET_NOTIFICATIONS':
      return {
        ...state,
        notificationsList: action.payload,
      };
    case 'SET_NOTIFICATIONS_COUNT':
      return {
        ...state,
        isNewNotification: action.payload,
      };
    case 'SET_COLLABORATOR_DETAILS':
      return {
        ...state,
        collaboratorDetails: action.payload,
      };
    case 'SET_COLLABORATOR_LIST':
      return {
        ...state,
        collaboratorList: action.payload,
      };
    case 'SET_COLLABORATOR_ACCEPT':
      return {
        ...state,
        collaboratorAcceptStatus: action.payload,
      };
    case 'SET_DISCOVERY_PROJECTS':
      return {
        ...state,
        discoveryProjects: action.payload,
      };
    case 'SET_COVER_IMG':
      return {
        ...state,
        coverImg: action.payload,
      };
    case 'ACTIVE_GOAL':
      return {
        ...state,
        leftSideActiveGoals: action.payload,
      };
    case 'ALL_PROJECT_COUNT':
      return {
        ...state,
        allProjectCount: action.payload,
      };
    case 'SET_SNAP_MODAL_STATUS':
      return {
        ...state,
        snapInfoModalStatus: action.payload,
      };
    case 'SET_COVER_IMG_UPLOAD_STATUS':
      return {
        ...state,
        coverImgUploadStatus: action.payload,
      };
    case 'SET_COLLABORATOR_INFO_MODAL_STATUS':
      return {
        ...state,
        collaboratorInfoModalStatus: action.payload,
      };
    case 'SET_LEFT_SIDE_BAR_TAB':
      return {
        ...state,
        activeTab: action.payload,
      };
    case 'SET_COLLAPSE_LEFT_SIDE_BAR_TAB':
      return {
        ...state,
        collapseActiveTab: action.payload,
      };

    case 'SET_MOOD_IMAGE_OPEN':
      return {
        ...state,
        isMoodImageOpen: action.payload,
      };

    case 'SET_SIDEBAR_STATUS':
      return {
        ...state,
        isSidebarOpen: action.payload,
      };
    case 'SET_CLICKED_ITEM_SIDEBAR':
      return {
        ...state,
        clickedItemSidebar: action.payload,
      };
    case 'SET_FILTER_DISCOVERY_DATA':
      return {
        ...state,
        filteredData: action.payload,
      };
    case 'SET_FILTERED_INFO_DATA':
      return {
        ...state,
        isFilteredDiscoveryInfo: action.payload,
      };
    default:
      return state;
  }
};

export default project;
