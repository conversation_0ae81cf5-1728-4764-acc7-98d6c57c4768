import './styles/global.scss';

import { Admin, Resource } from 'react-admin';
import { Route } from 'react-router-dom';
import { createTheme } from '@mui/material/styles';
import dataProvider from './providers';
import authProvider from './providers/authProvider';

// resourses
import LoginPage from './pages/Login/Login';
import UserList from './pages/User/index';
import ProjectList from './pages/Project/index';
import CalloutList from './pages/Callout/index';
import UserView from './pages/User/view';
import TagList from './pages/Tag/index';

import { ProjectsIcon, UsersIcon, TagsIcon, CallOutIcon, MarketplaceIcon } from './icon';
import ProjectView from './pages/Project/view';
import { ViewCallout } from './pages/Callout/view';
import EditCallOut from './pages/Callout/edit';
import CreateCallOut from './pages/Callout/create';
import CallOutCreate from './pages/Callout/createCallout';
import MarketplacePage from './pages/Marketplace/index';

const myTheme = createTheme({
  // ...defaultTheme,
  palette: {
    secondary: {
      main: '#05012d', // Secondary color (optional)
    },
  },
  sidebar: {
    width: 160,
    closedWidth: 50,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          backgroundColor: '#05012d',
          color: '#ffffff',
          '&:hover': {
            // backgroundColor: '#040124', // Darker shade on hover
          },
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#05012d', // Header color
          color: '#ffffff',
        },
      },
    },
  },
});

// };

const App = () => (
  <>
    <Admin
      title="Smash Cupid"
      theme={myTheme}
      authProvider={authProvider}
      dataProvider={dataProvider(process.env.REACT_APP_API_BASE_URL)}
      loginPage={LoginPage}
    >
      <Resource name="users" icon={UsersIcon} list={UserList}>
        <Route path=":id/show" element={<UserView />} />
      </Resource>

      <Resource name="projects" icon={ProjectsIcon} list={ProjectList}>
        <Route path=":id/show" element={<ProjectView />} />
      </Resource>
      <Resource
        name="callouts"
        icon={CallOutIcon}
        list={CalloutList}
        create={CreateCallOut}
      >
        <Route path=":id" element={<EditCallOut />} />
        <Route path=":id/create" element={<CallOutCreate />} />
        <Route path=":id/show" element={<ViewCallout />} />
      </Resource>
      <Resource name="tag" icon={TagsIcon} list={TagList} />
      <Resource name="marketplace" icon={MarketplaceIcon} list={MarketplacePage} options={{ label: 'Marketplace' }} />
    </Admin>
  </>
);

export default App;
