/**
 * this function will be used to get data from local storage
 */
const get = (key) => {
  let storageData = null;
  if (typeof window !== 'undefined') {
    storageData = localStorage.getItem(key);
    // Parse JSON data if it exists, otherwise return null
    if (storageData) {
      try {
        return JSON.parse(storageData);
      } catch (error) {
        // If parsing fails, return the raw string (for backward compatibility)
        return storageData;
      }
    }
  }
  return storageData;
};

const set = (key, data) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(key, JSON.stringify(data));
  }
};

const remove = (key) => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(key);
  }
};

module.exports = {
  get,
  set,
  remove,
};
