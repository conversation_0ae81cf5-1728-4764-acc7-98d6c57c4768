/**
 * this function will be used to get data from local storage
 */
const get = (key) => {
  let storageData = null;
  if (typeof window !== 'undefined') {
    storageData = localStorage.getItem(key);
    console.log(`🔍 Storage.get('${key}'):`, storageData ? 'EXISTS' : 'NULL');
    // Parse JSON data if it exists, otherwise return null
    if (storageData) {
      try {
        const parsed = JSON.parse(storageData);
        console.log(`🔍 Parsed data for '${key}':`, typeof parsed);
        return parsed;
      } catch (error) {
        // If parsing fails, return the raw string (for backward compatibility)
        console.log(`🔍 Parse failed for '${key}', returning raw string`);
        return storageData;
      }
    }
  }
  return storageData;
};

const set = (key, data) => {
  if (typeof window !== 'undefined') {
    console.log(`🔍 Storage.set('${key}'):`, typeof data);
    localStorage.setItem(key, JSON.stringify(data));
  }
};

const remove = (key) => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem(key);
  }
};

module.exports = {
  get,
  set,
  remove,
};
