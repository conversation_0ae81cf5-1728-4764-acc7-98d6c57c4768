import Storage from '../storage';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('Storage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('get', () => {
    it('should return null when localStorage returns null', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const result = Storage.get('test-key');
      
      expect(result).toBeNull();
      expect(localStorageMock.getItem).toHaveBeenCalledWith('test-key');
    });

    it('should parse JSON data correctly', () => {
      const testData = { user: 'test', role: 'admin' };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(testData));
      
      const result = Storage.get('test-key');
      
      expect(result).toEqual(testData);
    });

    it('should return raw string for non-JSON data (backward compatibility)', () => {
      const testString = 'simple-token-string';
      localStorageMock.getItem.mockReturnValue(testString);
      
      const result = Storage.get('test-key');
      
      expect(result).toBe(testString);
    });

    it('should handle malformed JSON gracefully', () => {
      const malformedJson = '{"incomplete": json';
      localStorageMock.getItem.mockReturnValue(malformedJson);
      
      const result = Storage.get('test-key');
      
      expect(result).toBe(malformedJson); // Should return raw string
    });

    it('should return null when window is undefined (SSR)', () => {
      const originalWindow = global.window;
      delete global.window;
      
      const result = Storage.get('test-key');
      
      expect(result).toBeNull();
      
      // Restore window
      global.window = originalWindow;
    });
  });

  describe('set', () => {
    it('should stringify and store data', () => {
      const testData = { user: 'test', role: 'admin' };
      
      Storage.set('test-key', testData);
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'test-key',
        JSON.stringify(testData)
      );
    });

    it('should handle string data', () => {
      const testString = 'simple-token';
      
      Storage.set('test-key', testString);
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'test-key',
        JSON.stringify(testString)
      );
    });

    it('should not call localStorage when window is undefined (SSR)', () => {
      const originalWindow = global.window;
      delete global.window;
      
      Storage.set('test-key', 'test-value');
      
      expect(localStorageMock.setItem).not.toHaveBeenCalled();
      
      // Restore window
      global.window = originalWindow;
    });
  });

  describe('remove', () => {
    it('should remove item from localStorage', () => {
      Storage.remove('test-key');
      
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('test-key');
    });

    it('should not call localStorage when window is undefined (SSR)', () => {
      const originalWindow = global.window;
      delete global.window;
      
      Storage.remove('test-key');
      
      expect(localStorageMock.removeItem).not.toHaveBeenCalled();
      
      // Restore window
      global.window = originalWindow;
    });
  });
});
