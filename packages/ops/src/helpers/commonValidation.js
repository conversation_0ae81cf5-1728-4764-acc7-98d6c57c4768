/* eslint-disable no-nested-ternary */
/* eslint-disable no-useless-escape */

import validator from 'validator';

export const required = (value) => {
  if (typeof value === 'string' && value.trim().length === 0) {
    return 'required field is empty';
  }
  return value || typeof value === 'number' ? undefined : 'Required';
};

//validate google location
export const validateLocation = (location) => {
  // If the location is an empty string, null, or undefined
  if (!location || typeof location !== 'string' || location.trim() === '') {
    return 'Please select a location from the dropdown.';
  }

  // Check if the location is formatted correctly by Google
  const isValidLocation = /[,]/.test(location);

  if (!isValidLocation) {
    return 'Please select a location from the dropdown.';
  }

  return undefined;
};

export const email = (value) =>
  value && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,6}$/i.test(value)
    ? 'Invalid email address'
    : undefined;

export const maxLength = (max) => (value) =>
  value && value.length > max ? `Must be ${max} characters or less` : undefined;
export const maxLength15 = maxLength(15);
export const maxLength20 = maxLength(20);
export const maxLength30 = maxLength(30);

export const minLength = (min) => (value) =>
  value && value.length < min ? `Must be ${min} characters or more` : undefined;
export const minLength4 = minLength(4);

export const formatter = new Intl.NumberFormat('en-GB', {
  style: 'currency',
  currency: 'GBP',
  minimumFractionDigits: 0,
});

export const phoneNumber = (value) =>
  value && !/((\+44(\s\(0\)\s|\s0\s|\s)?)|0)7\d{3}(\s)?\d{6}/g.test(value)
    ? 'Please enter a valid UK number'
    : undefined;

export const urlValidation = (value) =>
  value && !validator.isURL(value) ? 'Please enter a valid URL' : undefined;

export const pdfFile = (value) =>
  value && !/\.(pdf)$/i.test(value) ? 'Please enter a valid file' : undefined;

export const videoUrlValidation = (value) => {
  let validateMsg = '';
  if (value) {
    validateMsg =
      /^((?:https?:)?\/\/)?((?:www|m)\.)?((?:youtube\.com|youtu.be))(\/(?:[\w\-]+\?v=|embed\/|v\/)?)([\w\-]+)(\S+)?$/gm.test(
        value
      )
        ? undefined
        : !/^(http\:\/\/|https\:\/\/)?(www\.)?(vimeo\.com\/)([0-9]+)$/.test(
            value
          )
        ? 'Please enter correct youtube or vimeo url'
        : undefined;
  }
  return validateMsg;
};

/**
 * Validate image file
 *
 * @param {File} file - The image file to validate
 * @returns {string} Error message, if any
 */
export const validateImage = (file) => {
  // Check if a file is selected
  if (!file) {
    return 'No file selected.';
  }

  // Allowed image file types
  const allowedTypes = ['image/png', 'image/jpeg', 'image/gif'];

  // Check if the selected file type is allowed
  if (!allowedTypes.includes(file.type)) {
    return 'Please select a valid image file (png, jpeg, gif).';
  }

  // Return empty string if no error
  return '';
};

// (?:http|https)?:?\/?\/?(?:www\.|player\.)?vimeo\.com\/(?:channels\/(?:\w+\/)?|groups\/(?:[^\/]*)\/videos\/|video\/|)(\d+)(?:|\/\?)
