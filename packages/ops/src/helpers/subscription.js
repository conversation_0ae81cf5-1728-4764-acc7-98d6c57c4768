/**
 * @param {string} subscriptionlabel - Subscription status
 */

export const getSubscriptionLabel = (subsStatus) => {
  let badgeColor = '';
  let labelText = '';

  switch (subsStatus) {
    case 'pro':
      badgeColor = 'badge-primary';
      labelText = 'Pro';
      break;
    case 'trial':
      badgeColor = 'badge-primary';
      labelText = 'Pro Trial';
      break;
    case 'enterprise':
      badgeColor = 'badge-success';
      labelText = 'Enterprise';
      break;
    case 'legacy':
      badgeColor = 'badge-dark';
      labelText = 'Legacy';
      break;
    case 'free':
      badgeColor = 'badge-secondary';
      labelText = 'Free';
      break;
    default:
      badgeColor = '';
      labelText = '';
      break;
  }

  return { badgeColor, labelText };
};
