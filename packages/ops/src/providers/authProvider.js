import axios from 'axios';
import { jwtDecode } from 'jwt-decode';

import Storage from '../lib/storage';

const authProvider = {
  login: async ({ email, otp }) => {
    if (!otp) {
      return new Promise((resolve, reject) => {
        axios({
          url: `${process.env.REACT_APP_IM_BASE_URL}/v1/auth/sendOTPToEmail`,
          headers: {
            'Content-Type': 'application/json',
          },
          method: 'post',
          data: { email, authFlow: 'signin' },
          responseType: 'json',
        })
          .then((response) => {
            if (response.status === 200) {
              resolve({ otpSent: true });
            } else {
              reject(new Error('OTP request failed'));
            }
          })
          .catch((err) => {
            console.error('Error in OTP request:', err);
            reject(err.response?.data?.message || 'OTP request failed');
          });
      });
    } else {
      try {
        const response = await fetch(
          `${process.env.REACT_APP_IM_BASE_URL}/v1/auth/verifyOtp`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, otp }),
          }
        );

        if (!response.ok) {
          throw new Error('OTP request failed');
        }

        const userData = await response.json();

        // Validate admin role before storing token
        try {
          const roleResponse = await fetch(
            `${process.env.REACT_APP_API_BASE_URL}/v1/user/roles`,
            {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${userData.data.token}`,
              },
            }
          );

          const roleData = await roleResponse.json();
          if (!roleResponse.ok || !roleData.data.includes('admin')) {
            throw new Error('You are not an Admin');
          }
        } catch (roleError) {
          console.error('Admin role validation failed:', roleError);
          throw new Error('You are not an Admin');
        }

        // Only store token after admin validation passes
        Storage.set('token', userData.data.token);
        Storage.set('auth', userData); // Save auth details
        return userData;
      } catch (error) {
        console.error('Error in OTP verification:', error);
        throw new Error(error.message || 'OTP verification failed');
      }
    }
  },

  logout: () => {
    Storage.remove('auth');
    Storage.remove('token');
    return Promise.resolve();
  },

  checkAuth: async () => {
    const token = Storage.get('token');

    // If no token exists, reject authentication
    if (!token) {
      return Promise.reject('No token found');
    }

    let idToken = '';
    if (token) {
      idToken = token.replace(/"/g, '');
    }

    // Check if token is expired using local validation (like webapp does)
    try {
      const decoded = jwtDecode(idToken);
      const currentTime = Math.floor(Date.now() / 1000);

      if (decoded.exp && currentTime > decoded.exp) {
        // Token is expired, clear storage and reject
        Storage.remove('auth');
        Storage.remove('token');
        return Promise.reject('Token expired');
      }
    } catch (decodeError) {
      // If token can't be decoded, it's invalid
      Storage.remove('auth');
      Storage.remove('token');
      return Promise.reject('Invalid token');
    }

    // Token exists and is not expired - resolve without API call
    // This prevents network issues from causing unwanted logouts during page reloads
    // Admin role validation should be done during login, not during auth checks
    return Promise.resolve(token);
  },

  checkError: () => Promise.resolve(),
  getPermissions: () => Promise.resolve(),
};

export default authProvider;
