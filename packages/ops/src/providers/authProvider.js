import axios from 'axios';

import Storage from '../lib/storage';

const authProvider = {
  login: async ({ email, otp }) => {
    if (!otp) {
      return new Promise((resolve, reject) => {
        axios({
          url: `${process.env.REACT_APP_IM_BASE_URL}/v1/auth/sendOTPToEmail`,
          headers: {
            'Content-Type': 'application/json',
          },
          method: 'post',
          data: { email, authFlow: 'signin' },
          responseType: 'json',
        })
          .then((response) => {
            if (response.status === 200) {
              resolve({ otpSent: true });
            } else {
              reject(new Error('OTP request failed'));
            }
          })
          .catch((err) => {
            console.error('Error in OTP request:', err);
            reject(err.response?.data?.message || 'OTP request failed');
          });
      });
    } else {
      try {
        const response = await fetch(
          `${process.env.REACT_APP_IM_BASE_URL}/v1/auth/verifyOtp`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, otp }),
          }
        );

        if (!response.ok) {
          throw new Error('OTP request failed');
        }

        const userData = await response.json();
        Storage.set('token', userData.data.token);
        Storage.set('auth', userData); // Save auth details
        return userData;
      } catch (error) {
        console.error('Error in OTP verification:', error);
        throw new Error(error.message || 'OTP verification failed');
      }
    }
  },

  logout: () => {
    Storage.remove('auth');
    Storage.remove('token');
    return Promise.resolve();
  },

  checkAuth: async () => {
    const token = Storage.get('token');
    let idToken = '';
    if (token) {
      idToken = token.replace(/"/g, '');
    }

    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_BASE_URL}/v1/user/roles`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${idToken}`,
          },
        }
      );

      const responseData = await response.json();
      if (!response.ok || !responseData.data.includes('admin')) {
        return Promise.reject('You are not an Admin');
      }

      return Promise.resolve(token);
    } catch (error) {
      return Promise.reject('You are not an Admin');
    }
  },

  checkError: () => Promise.resolve(),
  getPermissions: () => Promise.resolve(),
};

export default authProvider;
