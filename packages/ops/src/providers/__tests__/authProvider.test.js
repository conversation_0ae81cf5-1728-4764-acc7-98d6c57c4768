// Mock axios before importing authProvider
jest.mock('axios', () => ({
  default: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
}));

// Mock the Storage module
jest.mock('../../lib/storage', () => ({
  get: jest.fn(),
  set: jest.fn(),
  remove: jest.fn(),
}));

import authProvider from '../authProvider';
import Storage from '../../lib/storage';

// Mock fetch globally
global.fetch = jest.fn();

describe('authProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    global.fetch.mockClear();
  });

  describe('checkAuth', () => {
    it('should reject when no token exists', async () => {
      Storage.get.mockReturnValue(null);

      await expect(authProvider.checkAuth()).rejects.toBe('No token found');
    });

    it('should resolve immediately when both token and auth data exist', async () => {
      const mockToken = 'mock-token';
      const mockAuth = { user: 'test' };

      Storage.get
        .mockReturnValueOnce(mockToken) // First call for token
        .mockReturnValueOnce(mockAuth); // Second call for auth

      const result = await authProvider.checkAuth();

      expect(result).toBe(mockToken);
      expect(global.fetch).not.toHaveBeenCalled(); // Should not make API call
    });

    it('should resolve with token even if API call fails (prevents unwanted logout)', async () => {
      const mockToken = 'mock-token';

      Storage.get
        .mockReturnValueOnce(mockToken) // First call for token
        .mockReturnValueOnce(null); // Second call for auth (no auth data)

      // Mock API call failure
      global.fetch.mockRejectedValue(new Error('Network error'));

      const result = await authProvider.checkAuth();

      expect(result).toBe(mockToken);
      expect(global.fetch).toHaveBeenCalledTimes(1);
    });

    it('should reject when API call returns non-admin user', async () => {
      const mockToken = 'mock-token';

      Storage.get
        .mockReturnValueOnce(mockToken) // First call for token
        .mockReturnValueOnce(null); // Second call for auth (no auth data)

      // Mock API call with non-admin response
      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ data: ['user'] }), // Not admin
      });

      await expect(authProvider.checkAuth()).rejects.toBe(
        'You are not an Admin'
      );
    });

    it('should resolve when API call returns admin user', async () => {
      const mockToken = 'mock-token';

      Storage.get
        .mockReturnValueOnce(mockToken) // First call for token
        .mockReturnValueOnce(null); // Second call for auth (no auth data)

      // Mock API call with admin response
      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ data: ['admin'] }),
      });

      const result = await authProvider.checkAuth();

      expect(result).toBe(mockToken);
    });
  });

  describe('checkError', () => {
    it('should reject for 401 errors', async () => {
      const error = { status: 401 };

      await expect(authProvider.checkError(error)).rejects.toBe(
        'Authentication failed'
      );
    });

    it('should reject for 403 errors', async () => {
      const error = { status: 403 };

      await expect(authProvider.checkError(error)).rejects.toBe(
        'Authentication failed'
      );
    });

    it('should resolve for network errors (500, etc.)', async () => {
      const error = { status: 500 };

      const result = await authProvider.checkError(error);
      expect(result).toBeUndefined();
    });

    it('should resolve for errors without status', async () => {
      const error = { message: 'Network error' };

      const result = await authProvider.checkError(error);
      expect(result).toBeUndefined();
    });
  });

  describe('logout', () => {
    it('should remove auth and token from storage', async () => {
      const result = await authProvider.logout();

      expect(Storage.remove).toHaveBeenCalledWith('auth');
      expect(Storage.remove).toHaveBeenCalledWith('token');
      expect(result).toBeUndefined();
    });
  });
});
