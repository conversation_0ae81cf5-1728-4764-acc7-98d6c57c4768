/* eslint-disable import/no-anonymous-default-export */
/* eslint-disable indent */
import queryString from 'query-string';
import { get } from 'lodash';
import {
  fetchUtils,
  GET_LIST,
  GET_ONE,
  GET_MANY,
  GET_MANY_REFERENCE,
  CREATE,
  UPDATE,
  UPDATE_MANY,
  DELETE,
  DELETE_MANY,
} from 'react-admin';
import {
  handleFilters,
  handleSorting,
  handleSearchQuery,
} from '../helpers/helper';
import Storage from '../lib/storage';
import { setResource, setParams, setAPIUrl } from './request';
import modifiedResponse from './response';

export default (apiUrl, httpClient = fetchUtils.fetchJson) => {
  const getQueryForParams = (params, fields = [], resource) => {
    const { page, perPage } = get(params, 'pagination', {});
    const { field, order } = get(params, 'sort', {});
    const { select } = get(params, 'meta', {});
    const { populate } = get(params, 'meta', {});
    const start = (page - 1) * perPage;

    if (params.filter.marketplace === 'marketplace') {
      params.filter.q = '';
      delete params.filter.marketplace;
    }

    const { q, ...filters } = params.filter || {};
    const noPagination = filters?.noPagination || false;
    if (noPagination) {
      delete filters.noPagination;
    }

    // Handle excludeZeroSnapshots logic
    const excludeZeroSnapshots = filters?.excludeZeroSnapshots;
    if (excludeZeroSnapshots) {
      delete filters.excludeZeroSnapshots;
    }

    const query = {
      $limit: noPagination ? 100000 : perPage,
      $skip: noPagination ? 0 : start,
      $sort: handleSorting(field, order),
      ...handleFilters(filters),
      ...handleSearchQuery(q, fields),
    };

    if (select) {
      query.$select = select;
    }
    if (populate) {
      query.$populate = populate;
    }

    // Add logic for excludeZeroSnapshots
    if (excludeZeroSnapshots) {
      query.where = queryString.stringify({ nonEmptySnapshotsOnly: true });
    }

    return query;
  };

  const cleanData = (params) => {
    const clean = { ...params.data };
    ['createdAt', 'updatedAt', 'deletedAt', 'isDeleted'].forEach(
      (key) => delete clean[key]
    );
    return JSON.stringify(clean);
  };

  const convertDataRequestToHTTP = async (type, resource, params) => {
    let url = '';
    const options = {};
    apiUrl = setAPIUrl(apiUrl, type, resource);
    resource = setResource(type, resource, params);
    params = await setParams(type, resource, params);
    switch (type) {
      case GET_LIST: {
        try {
          let queryParams = [];
          if (resource === 'v1/user/projects') {
            queryParams = [
              'regNo',
              'creator.username',
              'creator.email',
              'cover.title',
              'cover.director',
              'cover.producer',
              'cover.writer',
              'basicInfo.tags.text',
              'basicInfo.status',
              'basicInfo.format',
              'basicInfo.genre',
              'basicInfo.setting',
              'userMeta.type',
            ];
          } else if (resource === 'v1/user/ops/list') {
            queryParams = [
              'email',
              'profile.name.fullName',
              'profile.city.address',
              'userMeta.type',
            ];
          } else if (resource === 'v1/callout/list') {
            queryParams = ['name', 'discoverer.name', 'genres', 'status'];
          } else {
            queryParams = [
              'email',
              'profile.name.fullName',
              'profile.city.address',
              'userMeta.type',
              'creator.username',
              'cover.title',
            ];
          }

          let response = getQueryForParams(params, queryParams);

          if (resource === 'v1/user/ops/projects') {
            response = { ...response, 'creator.userId': params._id };
          } else if (resource === 'v1/user/ops/callouts') {
            response = { ...response, 'discoverer.id': params._id };
          } else if (resource === 'v1/project/ops/snapshot') {
            response = { ...response, projectId: params._id };
          } else if (resource === 'userSubmission') {
            const { page, perPage } = get(params, 'pagination', {});
            const start = (page - 1) * perPage;
            const { field, order } = get(params, 'sort', {});
            const queryStr = queryString.stringify({
              $sort: handleSorting(field, order),
              $limit: perPage,
              $skip: start,
              $select: '_id name status submissions',
              action: 'both',
              userId: params.filter.userId,
            });
            return {
              url: `${apiUrl}/v1/callout/submissions?${queryStr}`,
              options,
            };
          } else if (resource === 'v1/callout/submissions') {
            const { page, perPage } = get(params, 'pagination', {});
            const { field, order } = get(params, 'sort', {});
            const { select, calloutId } = get(params, 'meta', {});
            // const { populate } = get(params, 'meta', {});
            const start = (page - 1) * perPage;
            delete params.filter.calloutId;
            delete params.filter.select;
            if (params.filter.status === '') {
              delete params.filter.status;
            }
            // const { q, ...filters } = params.filter || {};
            // const noPagination = filters?.noPagination || false;
            const queryStr = queryString.stringify({
              $sort: handleSorting(field, order),
              $limit: perPage,
              $skip: start,
              $select: `_id name status ${select}`,
              action: select,
              _id: calloutId,
              ...(params?.filter?.status
                ? { 'submissions.status': 'new' }
                : {}),
            });
            return {
              url: `${apiUrl}/v1/callout/submissions?${queryStr}`,
              options,
            };
          } else if (resource === 'v1/marketplace/module/snapshots') {
            delete response.$sort;
            return {
              url: `${apiUrl}/${resource}/${params._id}?${JSON.stringify(response)}`,
              options,
            };
          }
          return {
            url: `${apiUrl}/${resource}?${queryString.stringify(response)}`,
            options,
          };
        } catch (error) {
          return Promise.reject(
            new Error(`Error fetching ${resource} data: ${error.message}`)
          );
        }
      }
      case GET_ONE:
        url =
          resource === 'v1/callout'
            ? `${apiUrl}/${resource}/${params.id}/calloutDetails`
            : params?.id
              ? `${apiUrl}/${resource}/${params.id}`
              : `${apiUrl}/${resource}`;
        break;
      case GET_MANY_REFERENCE:
        const query = getQueryForParams(params);
        query[params.target] = params.id;
        url = `${apiUrl}/${resource}?${queryString.stringify(query)}`;
        break;
      case UPDATE:
        url = get(params, 'endPoint', null)
          ? `${apiUrl}/${resource}/${params.id}/${get(params, 'endPoint', null)}`
          : resource === 'v1/marketplace/module/sorting'
            ? `${apiUrl}/${resource}` // No ID in the URL for this specific endpoint
            : params?.id
              ? `${apiUrl}/${resource}/${params.id}`
              : `${apiUrl}/${resource}`;

        if (
          resource.startsWith('v1/marketplace/module/sorting') ||
          resource.startsWith('v1/marketplace/module/project-snaps/sorting') ||
          resource.startsWith('v1/marketplace/module/status')
        ) {
          options.method = 'PUT';
        } else {
          options.method = 'PATCH';
        }

        const bodyData = params.data;

        // For module sorting, include the ID in the request body
        if (resource === 'v1/marketplace/module/sorting') {
          options.body = JSON.stringify({
            id: params.id,
            ...bodyData,
          });
        } else {
          options.body = get(params, 'endPoint', null)
            ? JSON.stringify(params.data)
            : JSON.stringify(bodyData);
        }
        break;
      case CREATE:
        url = `${apiUrl}/${resource}`;
        options.method = 'POST';
        options.body = cleanData(params);
        break;
      case DELETE:
        url =
          resource === 'v1/callout'
            ? `${apiUrl}/${resource}`
            : `${apiUrl}/${resource}/${params.id}`;
        options.method = 'DELETE';
        options.body = JSON.stringify({ id: [params.id] });
        break;
      case GET_MANY:
        url = `${apiUrl}/v1/user/ops/${params.ids[0]}`;
        options.method = 'GET';
        break;
      default:
        return Promise.reject(
          new Error(`Unsupported fetch action type ${type}`)
        );
    }

    return { url, options };
  };

  const convertHTTPResponse = (response, type, resource, params) => {
    if (!response || !response.json) {
      console.error(
        `Invalid response format for ${type} on ${resource}`,
        response
      );
      return Promise.reject(new Error(`Invalid response for ${resource}`));
    }
    const resp = modifiedResponse(response, type, resource, params);
    const { json } = response;
    switch (type) {
      case GET_LIST:
        if (!Array.isArray(resp)) {
          console.error(
            `Expected array in GET_LIST response for ${resource}`,
            resp
          );
          return Promise.reject(
            new Error(`Invalid GET_LIST response for ${resource}`)
          );
        }
        return {
          data: resp,
          // total: get(json, 'data.total', resp.length),
          total: get(
            json,
            'data.total',
            get(json, 'data.userList.total', resp.length)
          ),
        };
      case GET_ONE:
        if (resource.startsWith('v1/callout')) {
          return { data: resp.json.data.docs[0] };
        }
        if (!resp || typeof resp !== 'object') {
          console.error(
            `Expected object in GET_ONE response for ${resource}`,
            resp
          );
          return Promise.reject(
            new Error(`Invalid GET_ONE response for ${resource}`)
          );
        }
        return { data: resp.data };
      case GET_MANY:
        return resp;
      case GET_MANY_REFERENCE:
        return { data: resp, total: get(json, 'data.total', 0) };
      case CREATE:
        if (resource === 'v1/subscription/getStripeCheckoutLink') {
          json.data.id = json.data._id;
          return { data: json.data };
        } else if (resource === 'execute') {
          return { data: { id: params.data.id, data: json } };
        }
        if (resource === 'v1/user/create') {
          json.data.id = json.data.user._id;
          return { data: json.data };
        }
        if (resource === 'v1/marketplace/module') {
          json.data.id = json.data._id;
          return { data: json.data };
        }
        params.data.id = json.data._id;
        delete params.data._id;
        return { data: params.data };

      default:
        json.id = json.data._id;
        delete json._id;
        return { data: json };
    }
  };

  const setHeaders = async (options, resource) => {
    if (!options.headers) {
      options.headers = new Headers({ Accept: 'application/json' });
    }
    const token = Storage.get('token');
    let idToken = typeof token === 'string' ? token : token;
    options.headers.set('Authorization', `Bearer ${idToken}`);
    return options;
  };

  return async (type, resource, params) => {
    if (type === UPDATE_MANY) {
      return Promise.all(
        params.ids.map((id) =>
          httpClient(`${apiUrl}/${resource}/${id}`, {
            method: 'PATCH',
            body: JSON.stringify(params.data),
          }).then((response) => response.json())
        )
      ).then((responses) => ({ data: responses }));
    }
    if (type === DELETE_MANY) {
      return Promise.all(
        params.ids.map((id) =>
          httpClient(`${apiUrl}/${resource}/${id}`, {
            method: 'DELETE',
          }).then((response) => response.json())
        )
      ).then((responses) => ({ data: responses }));
    }
    try {
      let { url, options } = await convertDataRequestToHTTP(
        type,
        resource,
        params
      );
      options = await setHeaders(options, resource);
      const response = await httpClient(url, options);
      return convertHTTPResponse(response, type, resource, params);
    } catch (error) {
      return Promise.reject(error);
    }
  };
};
