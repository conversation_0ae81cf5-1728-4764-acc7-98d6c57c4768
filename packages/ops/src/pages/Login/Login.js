import { useNotify, useRedirect, useLogin } from 'react-admin';
import { useState } from 'react';
import InlineSVG from 'react-inlinesvg';
import { mergeCls } from '../../helpers';

const Login = () => {
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [otpSent, setOtpSent] = useState(false);
  const [isOtpIncorrect, setIsOtpIncorrect] = useState(false);
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const notify = useNotify();
  const login = useLogin();
  const redirect = useRedirect();

  const requestOtp = async () => {
    try {
      if (isSubmitting) return; // Prevent multiple submissions

      // // Set submitting state to true
      setIsSubmitting(true);
      await login({ email, authFlow: 'signIn' }, { method: 'sendOtp' });
      notify('OTP sent successfully', { type: 'success' });
      setOtpSent(true);
    } catch (error) {
      notify('Failed to send OTP, Please Verify Your Email Id.', {
        type: 'error',
      });
      setIsSubmitting(false);
    }
  };

  const verifyOtp = async () => {
    setIsSubmitting(false);

    setHasSubmitted(true);
    try {
      await login({ email, otp });
      setIsOtpIncorrect(false);
      redirect('/users');
    } catch (error) {
      setIsOtpIncorrect(true);
      notify('Invalid OTP', { type: 'error' });
    }
  };

  const onClickResend = async () => {
    try {
      await login({ email, authFlow: 'signIn' }, { method: 'sendOtp' });
      notify('OTP resent successfully', { type: 'success' });
    } catch (error) {
      notify(error.message || 'Failed to resend OTP', { type: 'error' });
    }
  };

  return (
    <>
      <div className="container-fluid backGroundImage bg">
        <div className="modal-container p-md-5 mt-5">
          <h3 className="mb-3 text-center text-primary fw-400 heading">
            Log in to Cupid
          </h3>

          <div className="d-flex flex-column">
            <p className="text-left paragraph mt-4">Email</p>
            <div className="position-relative">
              <input
                type="email"
                placeholder="Your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className={otpSent ? 'textLabelFilled' : 'textInput'}
                style={{
                  paddingRight: '30px',
                  backgroundColor: otpSent ? '#f1f1f1' : 'white',
                }}
                disabled={otpSent}
              />
              {otpSent && (
                <div
                  className="position-absolute"
                  style={{
                    right: '10px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                  }}
                >
                  <InlineSVG
                    className={mergeCls(['mr-2'])}
                    src="../../assets/svg/filled.svg"
                    height={20}
                    width={20}
                  />
                </div>
              )}
            </div>

            {!otpSent ? (
              <button
                type="button"
                onClick={requestOtp}
                isActive={!isSubmitting} // Disable when submitting
                disabled={isSubmitting}
                className="modalBtn mt-4"
              >
                Send verification code
              </button>
            ) : (
              <p className="text-center paragraph mt-4">
                Please enter the verification code we have sent to your email
                address.
              </p>
            )}

            {otpSent && (
              <>
                <input
                  type="text"
                  placeholder="Enter OTP"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  required
                  className={
                    isOtpIncorrect
                      ? 'textInput mt-2 errorClass'
                      : 'textInput mt-2'
                  }
                />
                <p data-cy="goToSignup" className="smallText mt-1">
                  {hasSubmitted && isOtpIncorrect ? (
                    <>
                      <span style={{ color: '#e11900' }}>
                        Wrong code entered.
                      </span>
                      <button
                        name="resend"
                        type="button"
                        className="link-btn p2"
                        onClick={onClickResend}
                      >
                        Resend code here.
                      </button>
                    </>
                  ) : (
                    <>
                      Didn’t receive your code?
                      <button
                        name="resend"
                        type="button"
                        className="link-btn p3"
                        onClick={onClickResend}
                      >
                        Resend code here.
                      </button>
                    </>
                  )}
                </p>
                <button
                  type="button"
                  onClick={verifyOtp}
                  className={` mt-4 mb-1 ${otp ? 'modalBtn ' : 'disabledBtn '}`}
                  disabled={!otp}
                >
                  SUBMIT
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default Login;
