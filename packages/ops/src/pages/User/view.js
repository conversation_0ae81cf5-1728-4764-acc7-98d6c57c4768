import React, { useState } from 'react';
import { Show, useGetOne, Title } from 'react-admin';
import { useParams } from 'react-router-dom';
import { Button, Box, Typography, Grid } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import { UserEditForm, OrganisationEditForm } from './userEditForm';
import { getSubscriptionLabel } from '../../helpers/subscription';
import ProjectTable from './projectTable';
import OrganisationTable from './organisationList';
import CalloutTable from './calloutTable';
import SubmissionList from './submissionTable';
import BasicBreadcrumbs from '../../sharedComponents/breadcrumb';

/**
 * A customized list component for displaying project data in the admin dashboard.
 *
 * @param {Object} props - Component properties.
 * @returns {JSX.Element} - React component.
 */
const UserView = () => {
  const { id } = useParams();
  const [isEditingOrg, setIsEditingOrg] = useState(false);
  const [isEditingProfile, setIsEditingProfile] = useState(false);

  const { data } = useGetOne('users', {
    id,
  });
  return (
    <>
      <Title title={`User: ${data?.userName}`} />
      <div className="py-4">
        <BasicBreadcrumbs
          links={[{ label: 'Users', url: '/users' }, { label: data?.userName }]}
        />

        <Show title={false}>
          <Box sx={{ border: '1px solid #ddd', borderRadius: '5px', p: 3 }}>
            <Typography sx={{ mb: 2 }} variant="h6" fontWeight="bold">
              User Details
            </Typography>

            {isEditingProfile ? (
              <UserEditForm
                user={data}
                onClose={() => setIsEditingProfile(false)}
                userId={id}
              />
            ) : (
              <>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box>
                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        sx={{ mb: 1 }}
                      >
                        User Name
                      </Typography>
                      <Typography variant="body2">
                        {data?.userName || 'N/A'}
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Box>
                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        sx={{ mb: 1 }}
                      >
                        Email
                      </Typography>
                      <Typography variant="body2">
                        {data?.email || 'N/A'}
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Box>
                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        sx={{ mb: 1 }}
                      >
                        Created Date
                      </Typography>
                      <Typography variant="body2">
                        {data?.created || 'N/A'}
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Box>
                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        sx={{ mb: 1 }}
                      >
                        Subscription
                      </Typography>
                      <Typography variant="body2">
                        {
                          getSubscriptionLabel(data?.subscriptionStatus)
                            .labelText
                        }
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>

                <Grid container spacing={2} mt={2}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box>
                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        sx={{ mb: 1 }}
                      >
                        Project Notifications
                      </Typography>
                      {data?.projectNotifications === 'True' ? (
                        <CheckCircleIcon className="text-success" />
                      ) : (
                        <CancelIcon className="text-danger" />
                      )}
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Box>
                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        sx={{ mb: 1 }}
                      >
                        Terms & Privacy
                      </Typography>
                      {data?.termsPrivacy === 'True' ? (
                        <CheckCircleIcon className="text-success" />
                      ) : (
                        <CancelIcon className="text-danger" />
                      )}
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Box>
                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        sx={{ mb: 1 }}
                      >
                        News & Updates
                      </Typography>
                      {data?.receiveMarketMaterial === 'True' ? (
                        <CheckCircleIcon className="text-success" />
                      ) : (
                        <CancelIcon className="text-danger" />
                      )}
                    </Box>
                  </Grid>
                </Grid>

                <Grid container spacing={2} mt={2}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box>
                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        sx={{ mb: 1 }}
                      >
                        Profile Image
                      </Typography>
                      {data?.profileImage ? (
                        <img
                          src={data.profileImage}
                          alt="Profile"
                          style={{
                            width: '100px',
                            height: '150px',
                            objectFit: 'cover',
                          }}
                        />
                      ) : (
                        <Typography variant="body2">
                          No Image Available
                        </Typography>
                      )}
                    </Box>
                  </Grid>
                </Grid>
              </>
            )}

            {!isEditingProfile && (
              <Button sx={{ mt: 2 }} onClick={() => setIsEditingProfile(true)}>
                Edit
              </Button>
            )}
          </Box>
        </Show>

        <Box sx={{ mt: 4, border: '1px solid #ddd', borderRadius: '5px' }}>
          <div className="mt-4">
            {isEditingOrg ? (
              <OrganisationEditForm
                user={data}
                onClose={() => setIsEditingOrg(false)}
              />
            ) : (
              <>
                <OrganisationTable
                  user={data}
                  isEditingOrg={isEditingOrg}
                  handleEditOrg={setIsEditingOrg}
                />
              </>
            )}
          </div>
        </Box>
        <ProjectTable />
        <SubmissionList />
        <CalloutTable />
      </div>
    </>
  );
};
export default UserView;
