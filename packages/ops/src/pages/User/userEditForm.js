import React, { useState } from 'react';
import {
  SimpleForm,
  TextInput,
  SelectInput,
  useNotify,
  useRefresh,
  useDataProvider,
  Toolbar,
  SaveButton,
  required,
} from 'react-admin';
import { DialogTitle, DialogContent, Button, Grid } from '@mui/material';
import Options from '../../configurations/option.json';
import LocationInput from '../../sharedComponents/locationInput';
import ImageUpload from '../../sharedComponents/imageUploader';
import { get } from 'lodash';

const UserEditForm = ({ user, onClose, userId }) => {
  const notify = useNotify();
  const refresh = useRefresh();
  const dataProvider = useDataProvider();
  const [location, setLocation] = useState({});
  const [profileImage, setProfileImage] = useState(
    get(user, 'profileImage', '')
  );
  const validateRequired = required();

  const handleSubmit = async (values) => {
    try {
      const payload = {
        profile: {
          name: {
            firstName: values.firstName,
            lastName: values.lastName,
            fullName: `${values.firstName} ${values.lastName}`,
          },
          city: location.city,
          occupationType: values.occupation,
          profileImage,
        },
      };
      await dataProvider.update('updateUser', { id: values.id, data: payload });
      notify('User details updated successfully');
      refresh();
      onClose();
    } catch (error) {
      notify('Error updating user details', { type: 'error' });
    }
  };

  const choices = Options.creatorOccupation.map((value) => ({
    id: value,
    name: value,
  }));

  const CustomToolbar = ({ onClose }) => (
    <Toolbar
      sx={{
        display: 'flex',
        justifyContent: 'flex-start',
        gap: 2,
        width: '100%',
      }}
    >
      <SaveButton />
      <Button onClick={onClose}>Cancel</Button>
    </Toolbar>
  );

  return (
    <>
      <DialogTitle
        sx={{
          fontWeight: 'bold',
          fontSize: '16px',
        }}
      >
        Edit User Details
      </DialogTitle>

      <DialogContent>
        <SimpleForm
          toolbar={<CustomToolbar onClose={onClose} />}
          defaultValues={{
            firstName: user?.firstName || '',
            lastName: user?.lastName || '',
            location: user?.city || '',
            occupation: user?.occupationType || '',
            profileImage: user?.profileImage || '',
          }}
          onSubmit={handleSubmit}
        >
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <TextInput
                source="firstName"
                label="First Name"
                fullWidth
                validate={validateRequired}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <TextInput
                source="lastName"
                label="Last Name"
                fullWidth
                validate={validateRequired}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <LocationInput
                id="autocomplete"
                source="location"
                label="Location"
                setLocation={setLocation}
                fullWidth
                size="large"
                validate={validateRequired}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <SelectInput
                source="occupation"
                label="Occupation"
                fullWidth
                choices={choices}
                emptyText={false}
                sx={{
                  marginTop: 0,
                }}
                validate={validateRequired}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <ImageUpload
                source="profileImage"
                label="Profile Image"
                maxSize={10000000}
                onUploadSuccess={setProfileImage}
                defaultValue={profileImage}
                userId={userId}
                validate={validateRequired}
              />
            </Grid>
          </Grid>
        </SimpleForm>
      </DialogContent>
    </>
  );
};

const OrganisationEditForm = ({ user, onClose }) => {
  const notify = useNotify();
  const refresh = useRefresh();
  const dataProvider = useDataProvider();
  const [orgLogo, setOrgLogo] = useState(get(user, 'organisationLogo', ''));
  const validateRequired = required();

  const handleSubmit = async (values) => {
    try {
      const payload = {
        profile: {
          organisation: values.organisation,
          organisationType: values.organisationType,
          discovererProfile: values.organisationBio,
          organisationLogo: orgLogo,
        },
      };
      await dataProvider.update('updateUser', {
        id: user.id,
        data: payload,
      });
      notify('Organisation details updated successfully');
      refresh();
      onClose();
    } catch (error) {
      notify('Error updating organisation details', { type: 'error' });
    }
  };

  const choices = Options.creatorOrg.map((value) => ({
    id: value,
    name: value,
  }));

  const CustomToolbar = ({ onClose }) => (
    <Toolbar
      sx={{
        display: 'flex',
        justifyContent: 'flex-start',
        gap: 2,
        width: '100%',
      }}
    >
      <SaveButton />
      <Button onClick={onClose}>Cancel</Button>
    </Toolbar>
  );

  return (
    <>
      <DialogTitle
        sx={{
          fontWeight: 'bold',
          fontSize: '16px',
        }}
      >
        Edit Organisation Details
      </DialogTitle>
      <DialogContent>
        <SimpleForm
          toolbar={<CustomToolbar onClose={onClose} />}
          defaultValues={{
            organisation: user?.organisationName || '',
            organisationType: user?.organisationType || '',
            organisationBio: user?.organisationBio || '',
            organisationLogo: user?.organisationLogo || '',
          }}
          onSubmit={handleSubmit}
        >
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextInput
                source="organisation"
                label="Organisation Name"
                fullWidth
                validate={validateRequired}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <SelectInput
                source="organisationType"
                label="Organisation Type"
                fullWidth
                choices={choices}
                emptyText={false}
                sx={{
                  marginTop: 0,
                }}
                validate={validateRequired}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextInput
                rows={10}
                source="organisationBio"
                label="Discoverer Profile"
                multiline
                fullWidth
                validate={validateRequired}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <ImageUpload
                defaultValue={orgLogo}
                source="organisationLogo"
                label="Organisation Logo"
                maxSize={10000000}
                onUploadSuccess={setOrgLogo}
                userId={user.id}
                validate={validateRequired}
              />
            </Grid>
          </Grid>
        </SimpleForm>
      </DialogContent>
      {/* <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
      </DialogActions> */}
    </>
  );
};

export { UserEditForm, OrganisationEditForm };
