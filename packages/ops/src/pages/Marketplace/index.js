// MarketplacePage.jsx - With immediate snapshot deletion
import React, { useState, useEffect, useCallback } from 'react';
import {
  List,
  Datagrid,
  TextField,
  DateField,
  TopToolbar,
  useRecordContext,
  useDataProvider,
  useNotify,
  SearchInput,
  FunctionField,
  Link,
  ChipField,
} from 'react-admin';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  TextField as MuiTextField,
  MenuItem,
  Box,
  Paper,
  Typography,
  Chip,
  Button,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  Delete as DeleteIcon,
  KeyboardArrowUp as UpIcon,
  KeyboardArrowDown as DownIcon,
  VisibilityOff as VisibilityOffIcon,
  Delete,
} from '@mui/icons-material';
import { BannerIcon, CardIcon } from '../../icon';
import {
  getStatusBackgroundColor,
  getStatusTextColor,
} from '../../helpers/helper';

// SnapshotsListView component using React-Admin components
const SnapshotsListView = ({
  moduleId,
  data,
  deleteSnapshot,
  updateModuleSnapshots,
}) => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const [isOpen, setIsOpen] = useState(false);
  const [record, setRecord] = useState(false);

  const handleClickOpen = (record) => {
    setRecord(record);
    setIsOpen(true);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleConfirm = () => {
    setIsOpen(false);
    deleteSnapshot(moduleId, record);
  };

  // Move snapshot up in list
  const moveSnapshotUp = async (record, index) => {
    if (index === 0) return; // Already at the top

    // Create a deep copy of the data array
    const updatedSnapshots = JSON.parse(JSON.stringify(data));

    // Remove the snapshot from current position and insert at new position
    const [movedSnapshot] = updatedSnapshots.splice(index, 1);
    updatedSnapshots.splice(index - 1, 0, movedSnapshot);

    // Update the local state immediately for responsive UI
    updateModuleSnapshots(moduleId, updatedSnapshots);

    try {
      await dataProvider.update('v1/marketplace/module/project-snaps/sorting', {
        data: {
          moduleId: moduleId,
          sortingData: updatedSnapshots.map((snapshot, idx) => ({
            projectSnapId: snapshot._id,
            sortOrder: idx + 1,
          })),
        },
      });

      notify('Snapshot order updated', { type: 'success' });
    } catch (error) {
      notify('Error updating snapshot order', { type: 'error' });
      console.error('Error updating snapshot order:', error);

      // Revert the local state if API call fails
      updateModuleSnapshots(moduleId, data);
    }
  };

  // Move snapshot down in list
  const moveSnapshotDown = async (record, index) => {
    if (index === data.length - 1) return; // Already at the bottom

    // Create a deep copy of the data array
    const updatedSnapshots = JSON.parse(JSON.stringify(data));

    // Remove the snapshot from current position and insert at new position
    const [movedSnapshot] = updatedSnapshots.splice(index, 1);
    updatedSnapshots.splice(index + 1, 0, movedSnapshot);

    // Update the local state immediately for responsive UI
    updateModuleSnapshots(moduleId, updatedSnapshots);

    try {
      await dataProvider.update('v1/marketplace/module/project-snaps/sorting', {
        data: {
          moduleId: moduleId,
          sortingData: updatedSnapshots.map((snapshot, idx) => ({
            projectSnapId: snapshot._id,
            sortOrder: idx + 1,
          })),
        },
      });

      notify('Snapshot order updated', { type: 'success' });
    } catch (error) {
      notify('Error updating snapshot order', { type: 'error' });
      console.error('Error updating snapshot order:', error);

      // Revert the local state if API call fails
      updateModuleSnapshots(moduleId, data);
    }
  };

  // Custom Fields for React-Admin
  const PositionField = () => {
    const record = useRecordContext();
    return <span>{record ? record.index + 1 : ''}</span>;
  };

  const SnapshotActionsField = (props) => {
    const record = useRecordContext(props);
    if (!record) return null;

    // Find the index of this record in the data array
    const index = data.findIndex((item) => item._id === record._id);

    return (
      <>
        <Box sx={{ display: 'flex' }}>
          <IconButton
            onClick={(e) => {
              e.stopPropagation();
              moveSnapshotUp(record, index);
            }}
            disabled={index === 0}
          >
            <UpIcon style={{ color: index === 0 ? '#ccc' : '#0D0D3F' }} />
          </IconButton>
          <IconButton
            onClick={(e) => {
              e.stopPropagation();
              moveSnapshotDown(record, index);
            }}
            disabled={index === data.length - 1}
          >
            <DownIcon
              style={{ color: index === data.length - 1 ? '#ccc' : '#0D0D3F' }}
            />
          </IconButton>
          <IconButton
            onClick={(e) => {
              e.stopPropagation();
              handleClickOpen(record);
            }}
          >
            <Delete style={{ color: '#0D0D3F' }} />
          </IconButton>
        </Box>
      </>
    );
  };

  // Custom date field
  const FormattedDateField = (props) => {
    const record = useRecordContext(props);
    if (!record || !record.createdAt) return null;
    return <span>{new Date(record.createdAt).toLocaleDateString()}</span>;
  };

  const indexedData = data.map((item, idx) => ({
    ...item,
    index: idx,
  }));

  return (
    <>
      <Dialog open={isOpen} onClose={handleClose}>
        <DialogTitle sx={{ fontWeight: 700 }}>Confirm Deletion</DialogTitle>
        <DialogContent>
          Are you sure you want to delete this snapshot?
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} color="primary">
            Cancel
          </Button>
          <Button onClick={handleConfirm} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
      <Datagrid
        data={indexedData}
        rowClick={false}
        bulkActionButtons={false}
        pagination={true}
      >
        <PositionField label="" />
        <FunctionField
          sortable={false}
          source="title"
          label={<span style={{ fontWeight: 'bold' }}>Snapshot name</span>}
          render={(record) => (
            <Link
              className="admin-link"
              to={`${process.env.REACT_APP_WEBAPP_URL}/project/snap/${record.hash}`}
              target="_blank"
              rel="noopener noreferrer"
            >
              {record.notes}
            </Link>
          )}
        />
        <FunctionField
          sortable={false}
          source="projectName"
          label={<span style={{ fontWeight: 'bold' }}>Project name</span>}
          render={(record) => (
            <Link
              className="admin-link"
              to={`/projects/${record.projectId}/show`}
            >
              {record.projectName}
            </Link>
          )}
        />
        <TextField
          source="genre"
          label={<span style={{ fontWeight: 'bold' }}>Genre</span>}
        />
        <FunctionField
          sortable={false}
          source="creatorName"
          label={<span style={{ fontWeight: 'bold' }}>Project creator</span>}
          render={(record) => (
            <Link className="admin-link" to={`/users/${record.creatorId}/show`}>
              {record.creatorName}
            </Link>
          )}
        />
        <FunctionField
          label={<span style={{ fontWeight: 'bold' }}>Subscription</span>}
          render={(record) =>
            record ? (
              <ChipField
                source="subscriptionType"
                record={record}
                style={{
                  backgroundColor: getStatusBackgroundColor(
                    record.subscriptionType
                  ),
                  textTransform: 'capitalize',
                  color: getStatusTextColor(record.subscriptionType),
                  fontWeight: '700',
                }}
              />
            ) : null
          }
        />
        <FormattedDateField
          source="createdAt"
          label={<span style={{ fontWeight: 'bold' }}>Date created</span>}
        />
        <SnapshotActionsField
          label={<span style={{ fontWeight: 'bold' }}>Actions</span>}
        />
      </Datagrid>
    </>
  );
};

// ModuleCard component - Removed drag and drop implementation
const ModuleCard = ({
  module,
  index,
  isFirst,
  isLast,
  onEdit,
  onDelete,
  onTogglePublish,
  onAddSnapshot,
  handleOpenProjectModal,
  onMove,
  updateModuleSnapshots,
  setModules,
}) => {
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const [open, setOpen] = useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleConfirm = () => {
    setOpen(false);
    onDelete(); // call your delete logic
  };

  const deleteSnapshot = async (moduleId, record) => {
    try {
      await dataProvider.create('v1/marketplace/module/project-snap/remove', {
        data: {
          moduleId: moduleId,
          projectSnapId: record._id,
        },
      });

      // Update local state immediately
      setModules((prevModules) =>
        prevModules.map((module) => {
          if (module.id === moduleId) {
            const updatedSnapshots = module.projectSnaps.filter(
              (snap) => snap._id !== record._id
            );
            return { ...module, projectSnaps: updatedSnapshots };
          }
          return module;
        })
      );

      notify('Snapshot removed successfully', { type: 'success' });
      // No need to call fetchModules() anymore
    } catch (error) {
      notify('Error removing snapshot', { type: 'error' });
      console.error('Error removing snapshot:', error);
    }
  };

  // Custom Empty Component for modules that have no snapshots
  const EmptyModuleMessage = ({ moduleId, handleOpenProjectModal }) => (
    <Box sx={{ p: 2, textAlign: 'center' }}>
      <Typography variant="body1" color="textSecondary">
        No snapshots added to this module yet
      </Typography>
      <Button
        startIcon={<AddIcon />}
        onClick={() => handleOpenProjectModal(moduleId)}
        sx={{ mt: 1 }}
      >
        Add Snapshot
      </Button>
    </Box>
  );

  return (
    <Paper
      elevation={8}
      sx={{
        mb: 3,
        overflow: 'hidden',
      }}
    >
      <Dialog open={open} onClose={handleClose}>
        <DialogTitle sx={{ fontWeight: 700 }}>Confirm Deletion</DialogTitle>
        <DialogContent>
          Are you sure you want to delete this module?
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} color="primary">
            Cancel
          </Button>
          <Button onClick={handleConfirm} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
      <Box
        sx={{
          p: 2,
          bgcolor: 'background.paper',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: 1,
          borderColor: 'divider',
        }}
      >
        <Box
          display="flex"
          alignItems="center"
          sx={{
            minWidth: 0, // Allow content to shrink
            flexShrink: 1,
            maxWidth: '60%', // Limit module info width to leave space for buttons
          }}
        >
          {module.type === 'banner' ? <BannerIcon /> : <CardIcon />}
          <Typography
            variant="h6"
            sx={{
              mr: 1,
              ml: 2,
              fontWeight: 700,
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {module.name}
          </Typography>
          <Chip
            label={module.status === 'draft' ? 'Draft' : 'Public'}
            size="small"
            sx={{
              ml: 1,
              fontWeight: 700,
              backgroundColor: '#0D0D3F',
              color: '#FFFFFF',
              flexShrink: 0, // Prevent chip from shrinking
            }}
          />
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexShrink: 0, // Prevent buttons from shrinking
            ml: 2, // Add margin to separate from module name area
          }}
        >
          <IconButton onClick={onAddSnapshot} title="Add Snapshot">
            <AddIcon style={{ color: '#0D0D3F' }} />
          </IconButton>
          <IconButton onClick={onEdit} title="Edit Module">
            <EditIcon style={{ color: '#0D0D3F' }} />
          </IconButton>
          <IconButton
            onClick={() => onMove(module.id, index - 1, true)}
            disabled={isFirst}
            title="Move Up"
          >
            <UpIcon style={{ color: isFirst ? '#ccc' : '#0D0D3F' }} />
          </IconButton>
          <IconButton
            onClick={() => onMove(module.id, index + 1, true)}
            disabled={isLast}
            title="Move Down"
          >
            <DownIcon style={{ color: isLast ? '#ccc' : '#0D0D3F' }} />
          </IconButton>
          <IconButton
            onClick={onTogglePublish}
            title={module.status !== 'draft' ? 'Unpublish' : 'Publish'}
            disabled={module.projectSnaps.length === 0}
          >
            {module.status !== 'draft' ? (
              <VisibilityOffIcon
                style={{
                  color:
                    module.projectSnaps.length === 0
                      ? 'rgba(0, 0, 0, 0.26)'
                      : '#0D0D3F',
                }}
              />
            ) : (
              <VisibilityIcon
                style={{
                  color:
                    module.projectSnaps.length === 0
                      ? 'rgba(0, 0, 0, 0.26)'
                      : '#0D0D3F',
                }}
              />
            )}
          </IconButton>
          <IconButton
            onClick={handleClickOpen}
            title="Delete Module"
            color="error"
          >
            <DeleteIcon style={{ color: '#0D0D3F' }} />
          </IconButton>
        </Box>
      </Box>

      {module.projectSnaps.length === 0 ? (
        <EmptyModuleMessage
          moduleId={module.id}
          handleOpenProjectModal={handleOpenProjectModal}
        />
      ) : (
        <SnapshotsListView
          moduleId={module.id}
          data={module.projectSnaps}
          deleteSnapshot={deleteSnapshot}
          updateModuleSnapshots={updateModuleSnapshots}
        />
      )}
    </Paper>
  );
};

// MarketplacePage component
const MarketplacePage = () => {
  const [modules, setModules] = useState([]);
  const [isSnapshotLoading, setIsSnapshotLoading] = useState(false);
  const [openModuleModal, setOpenModuleModal] = useState(false);
  const [openProjectModal, setOpenProjectModal] = useState(false);
  const [currentModule, setCurrentModule] = useState(null);
  const [projectId, setProjectId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [selectedSnapId, setSelectedSnapId] = useState([]);
  const [isSnapSelected, setIsSnapSelected] = useState(false);

  const [moduleFormData, setModuleFormData] = useState({
    name: '',
    type: 'card',
  });
  const [projectsList, setProjectsList] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  // const [isClicked, setIsClicked] = useState(false);
  const dataProvider = useDataProvider();
  const notify = useNotify();

  // Function to update snapshots for a specific module
  const updateModuleSnapshots = (moduleId, updatedSnapshots) => {
    setModules((prevModules) =>
      prevModules.map((module) =>
        module.id === moduleId
          ? { ...module, projectSnaps: updatedSnapshots }
          : module
      )
    );
  };

  const fetchModules = useCallback(async () => {
    setLoading(true);
    try {
      const { data } = await dataProvider.getList('module', {
        pagination: { page: 1, perPage: 100 },
        filter: {},
      });
      setModules(data);
    } catch (error) {
      notify('Error loading modules', { type: 'error' });
      console.error('Error loading modules:', error);
    } finally {
      setLoading(false);
    }
  }, [dataProvider, notify]);

  useEffect(() => {
    fetchModules();
  }, [fetchModules]);

  const fetchProjects = async () => {
    try {
      const { data } = await dataProvider.getList('projects', {
        pagination: { page: 1, perPage: 500 },
        sort: { field: 'created_at', order: 'DESC' },
      });
      setProjectsList(data);
    } catch (error) {
      notify('Error loading projects', { type: 'error' });
      console.error('Error loading projects:', error);
    }
  };

  const fetchProjectSnapshots = async (projectId) => {
    setProjectId(projectId);
    try {
      // Your implementation here
    } catch (error) {
      notify('Error loading snapshots', { type: 'error' });
      console.error('Error loading snapshots:', error);
    }
  };

  const handleOpenModuleModal = (module = null) => {
    if (module) {
      setModuleFormData({ name: module.name, type: module.type });
      setCurrentModule(module);
    } else {
      setModuleFormData({ name: '', type: 'card' });
      setCurrentModule(null);
    }
    setOpenModuleModal(true);
  };

  const handleCloseModuleModal = () => {
    setOpenModuleModal(false);
    setModuleFormData({ name: '', type: 'card' });
    setCurrentModule(null);
  };

  const handleOpenProjectModal = (moduleId) => {
    setCurrentModule(modules.find((m) => m.id === moduleId));
    fetchProjects();
    setOpenProjectModal(true);
  };

  const handleCloseProjectModal = () => {
    setOpenProjectModal(false);
    setSelectedSnapId([]);
    setSelectedProject(null);
    if (isSnapSelected) {
      setIsSnapSelected(false);
      fetchModules();
    }
  };

  const handleModuleFormChange = (e) => {
    const { name, value } = e.target;
    setModuleFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleModuleSubmit = async () => {
    try {
      if (currentModule) {
        // Update existing module
        await dataProvider.update(
          `v1/marketplace/module/${currentModule._id}`,
          {
            data: {
              name: moduleFormData.name,
              type: moduleFormData.type,
            },
          }
        );

        // Update the module in local state
        setModules((prevModules) =>
          prevModules.map((module) =>
            module.id === currentModule.id
              ? {
                  ...module,
                  name: moduleFormData.name,
                  type: moduleFormData.type,
                }
              : module
          )
        );

        notify('Module updated', { type: 'success' });
      } else {
        // Create new module
        const response = await dataProvider.create('v1/marketplace/module', {
          data: {
            id: moduleFormData._id,
            name: moduleFormData.name,
            type: moduleFormData.type,
          },
        });
        const newModule = response.data;
        setModules((prevModules) => [...prevModules, newModule]);

        notify('Module created', { type: 'success' });

        // For new modules only, scroll to the bottom to show the newly added module
        setTimeout(() => {
          window.scrollTo({
            top: document.body.scrollHeight,
            behavior: 'smooth',
          });
        }, 500);
      }

      handleCloseModuleModal();
    } catch (error) {
      notify('Error saving module', { type: 'error' });
      console.error('Error saving module:', error);
    }
  };

  const handleProjectSelect = async (projectId) => {
    setSelectedProject(projectId);
    setIsSnapshotLoading(true);
    await fetchProjectSnapshots(projectId);
    setTimeout(() => {
      setIsSnapshotLoading(false);
    }, 1000);
  };

  const handleSnapshotSelect = async (snapshot) => {
    try {
      // setIsClicked(true);
      setSelectedSnapId((prev) => [...prev, snapshot]);
      await dataProvider.create('v1/marketplace/module/project-snap/add', {
        data: {
          moduleId: currentModule?.id,
          projectSnapId: snapshot,
        },
      });
      notify('Snapshot added to module', { type: 'success' });
      // handleCloseProjectModal();
      // setIsClicked(false);
      // fetchModules();
    } catch (error) {
      notify('Error adding snapshot', { type: 'error' });
      console.error('Error adding snapshot:', error);
    }
  };

  const handleTogglePublish = async (moduleId) => {
    try {
      const module = modules.find((m) => m.id === moduleId);
      const newStatus = module.status === 'draft' ? 'published' : 'draft';

      await dataProvider.update(`v1/marketplace/module/status/${moduleId}`, {
        data: { status: newStatus },
      });

      notify(
        `Module ${newStatus === 'published' ? 'published' : 'unpublished'}`,
        { type: 'success' }
      );
      fetchModules();
    } catch (error) {
      notify('Error updating module', { type: 'error' });
      console.error('Error updating module:', error);
    }
  };

  // Simplified moveModule function without drag and drop
  const moveModule = async (id, newIndex, shouldCallApi) => {
    // Find the current module and its index
    const moduleIndex = modules.findIndex((m) => m.id === id);
    if (moduleIndex === -1) return;

    // Validate the new index is within bounds
    if (newIndex < 0 || newIndex >= modules.length) return;

    // Make a copy of the modules array
    const newModules = [...modules];

    // Remove the module from its current position
    const [movedModule] = newModules.splice(moduleIndex, 1);

    // Insert the module at the new position
    newModules.splice(newIndex, 0, movedModule);

    // Update order property for all modules
    const updatedModules = newModules.map((module, index) => ({
      ...module,
      order: index + 1,
    }));

    // Update local state immediately
    setModules(updatedModules);

    if (shouldCallApi) {
      try {
        await dataProvider.update('v1/marketplace/module/sorting', {
          data: {
            sortingData: updatedModules.map((module) => ({
              id: module.id,
              sortOrder: module.order,
            })),
          },
        });

        notify('Module order updated', { type: 'success' });
      } catch (error) {
        notify('Error updating module order', { type: 'error' });
        console.error('Error updating module order:', error);

        // Revert changes on error
        fetchModules();
      }
    }
  };

  const handleDeleteModule = async (moduleId) => {
    try {
      await dataProvider.delete('v1/marketplace/module', {
        id: moduleId,
      });

      notify('Module deleted', { type: 'success' });
      fetchModules();
    } catch (error) {
      notify('Error deleting module', { type: 'error' });
      console.error('Error deleting module:', error);
    }
  };

  // Top toolbar with Add Module button
  const MarketplaceToolbar = () => (
    <TopToolbar sx={{ my: 3 }}>
      <Button
        variant="contained"
        startIcon={<AddIcon />}
        onClick={() => handleOpenModuleModal()}
      >
        Add Module
      </Button>
    </TopToolbar>
  );

  const projectFilter = [
    <SearchInput fullWidth key="project-search" source="q" alwaysOn />,
  ];

  const SelectButton = ({ onClick }) => {
    const record = useRecordContext();
    if (!record) return null;
    return (
      <Button
        variant="contained"
        onClick={() => onClick(record.id)}
        color="primary"
        disabled={record.totalSnapshot === 0}
        sx={{
          ...(record.totalSnapshot === 0 && {
            color: 'rgba(0, 0, 0, 0.26)',
          }),
        }}
      >
        Select
      </Button>
    );
  };

  const SelectSnapshotButton = ({ selectedModule, onClick }) => {
    const record = useRecordContext();
    if (!record) return null;
    const isAlreadySelected = selectedModule?.projectSnaps?.some(
      (snap) => snap._id === record.id
    );
    const isDisabled = isAlreadySelected;
    const isSelected = selectedSnapId.includes(record.id);
    return (
      <Button
        variant="contained"
        onClick={() => {
          onClick(record.id);
          setIsSnapSelected(true);
        }}
        color="primary"
        disabled={isDisabled || isSelected}
        sx={{
          ...((isDisabled || isSelected) && {
            color: 'rgba(2, 1, 1, 0.26)',
          }),
        }}
      >
        {isDisabled || isSelected ? 'Already added' : 'Select'}
      </Button>
    );
  };

  return (
    <Box>
      <MarketplaceToolbar />

      {loading ? (
        <Paper sx={{ p: 3, textAlign: 'center' }} elevation={8}>
          <Typography variant="body1">Loading modules...</Typography>
        </Paper>
      ) : (
        <>
          {modules.map((module, index) => (
            <ModuleCard
              key={module.id}
              module={module}
              index={index}
              isFirst={index === 0}
              isLast={index === modules.length - 1}
              onEdit={() => handleOpenModuleModal(module)}
              onDelete={() => handleDeleteModule(module.id)}
              onTogglePublish={() => handleTogglePublish(module.id)}
              onAddSnapshot={() => handleOpenProjectModal(module.id)}
              handleOpenProjectModal={() => handleOpenProjectModal(module.id)}
              onMove={moveModule}
              updateModuleSnapshots={updateModuleSnapshots}
              setModules={setModules}
            />
          ))}

          {modules.length === 0 && (
            <Paper sx={{ p: 3, textAlign: 'center' }} elevation={8}>
              <Typography variant="body1" gutterBottom>
                No modules created yet
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => handleOpenModuleModal()}
                sx={{ mt: 1 }}
              >
                Add Your First Module
              </Button>
            </Paper>
          )}
        </>
      )}

      {/* Add/Edit Module Dialog */}
      <Dialog
        open={openModuleModal}
        onClose={handleCloseModuleModal}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ fontWeight: 700 }}>
          {currentModule ? 'Edit Module' : 'Add New Module'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 1 }}>
            <MuiTextField
              margin="normal"
              fullWidth
              label="Module Name"
              name="name"
              value={moduleFormData.name}
              onChange={handleModuleFormChange}
              autoFocus
              required
            />
            <MuiTextField
              select
              margin="normal"
              fullWidth
              label="Module Type"
              name="type"
              value={moduleFormData.type}
              onChange={handleModuleFormChange}
              required
            >
              <MenuItem value="card">
                <span style={{ marginRight: '15px' }}>
                  <CardIcon />
                </span>{' '}
                Carousel
              </MenuItem>
              <MenuItem value="banner">
                <span style={{ marginRight: '15px' }}>
                  <BannerIcon />
                </span>
                Banner
              </MenuItem>
            </MuiTextField>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseModuleModal} color="inherit">
            Cancel
          </Button>
          <Button
            onClick={handleModuleSubmit}
            variant="contained"
            disabled={!moduleFormData.name}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Select Project Dialog */}
      <Dialog
        open={openProjectModal}
        onClose={handleCloseProjectModal}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle sx={{ fontWeight: 700 }}>
          {selectedProject ? 'Select Snapshot' : 'Select Project for Module'}
        </DialogTitle>
        <DialogContent dividers>
          {!selectedProject ? (
            <List
              resource="projects"
              hasCreate={false}
              actions={false}
              filters={projectFilter}
              filter={{ excludeZeroSnapshots: true }}
              data={projectsList}
              component="div"
              // Use loadingIndicator to show while data is loading
              loadingIndicator={
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  minHeight="200px"
                >
                  <CircularProgress />
                </Box>
              }
            >
              <Datagrid bulkActionButtons={false}>
                <FunctionField
                  source="projectName"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Project name</span>
                  }
                  render={(record) => (
                    <Link
                      className="admin-link"
                      to={`/projects/${record.id}/show`}
                    >
                      {record.projectName}
                    </Link>
                  )}
                />
                <TextField
                  source="genre"
                  label={<span style={{ fontWeight: 'bold' }}>Genre</span>}
                />
                <FunctionField
                  source="creatorName"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Creator name</span>
                  }
                  render={(record) => (
                    <Link
                      className="admin-link"
                      to={`/users/${record.projectCreatorId}/show`}
                    >
                      {record.creatorName}
                    </Link>
                  )}
                />
                <FunctionField
                  label={
                    <span style={{ fontWeight: 'bold' }}>Subscription</span>
                  }
                  render={(record) =>
                    record ? (
                      <ChipField
                        source="subscription"
                        record={record}
                        style={{
                          backgroundColor: getStatusBackgroundColor(
                            record.subscription
                          ),
                          textTransform: 'capitalize',
                          color: getStatusTextColor(record.subscription),
                          fontWeight: '700',
                        }}
                      />
                    ) : null
                  }
                />
                <DateField
                  source="created"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Date created</span>
                  }
                />
                <SelectButton onClick={handleProjectSelect} />
              </Datagrid>
            </List>
          ) : isSnapshotLoading ? (
            <Box
              display="flex"
              justifyContent="center"
              alignItems="center"
              minHeight="200px"
            >
              <CircularProgress />
            </Box>
          ) : (
            <List
              resource="snapshots"
              hasCreate={false}
              actions={false}
              filter={{
                id: projectId,
                marketplace: 'marketplace',
              }}
              component="div"
            >
              <Datagrid bulkActionButtons={false}>
                <TextField
                  source="snapshotTitle"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Snapshot name</span>
                  }
                />
                <DateField
                  source="created"
                  label={
                    <span style={{ fontWeight: 'bold' }}>Date created</span>
                  }
                />
                <SelectSnapshotButton
                  selectedModule={currentModule}
                  onClick={handleSnapshotSelect}
                />
              </Datagrid>
            </List>
          )}
        </DialogContent>
        <DialogActions>
          {selectedProject && (
            <Button
              onClick={() => setSelectedProject(null)}
              color="inherit"
              sx={{ mr: 'auto' }}
            >
              Back to Projects
            </Button>
          )}
          <Button onClick={handleCloseProjectModal} color="inherit">
            {isSnapSelected ? 'Done' : 'Cancel'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MarketplacePage;
