import React from 'react';
import { get } from 'lodash';
import { Link, useParams } from 'react-router-dom';
import {
  List,
  Datagrid,
  TextField,
  FunctionField,
  useGetOne,
  TopToolbar,
} from 'react-admin';
import { Box, Stack, Typography } from '@mui/material';
import InboxIcon from '@mui/icons-material/Inbox';
import Badge from '../../sharedComponents/badge';

const OtherProjectList = (props) => {
  const { id } = useParams();

  const { data, isLoading } = useGetOne('projects', { id });
  if (isLoading) return <div>Loading...</div>;

  const CustomToolbar = () => (
    <TopToolbar sx={{ minHeight: '10px !important' }} />
  );

  return (
    <Box sx={{ mt: 4, p: 2, border: '1px solid #ddd', borderRadius: '5px' }}>
      <Stack
        direction="row"
        spacing={2}
        sx={{ justifyContent: 'space-between' }}
      >
        <Typography variant="subtitle1" fontWeight="bold">
          Other projects from creator
        </Typography>
      </Stack>
      <List
        resource="projectList"
        exporter={false}
        filter={{
          id: data.creatorId,
        }}
        disableSyncWithLocation
        title={false}
        empty={
          <>
            <Box textAlign="center" p={2}>
              <InboxIcon sx={{ fontSize: 100 }} />
              <Typography variant="h6" color="textSecondary">
                No Project Yet
              </Typography>
            </Box>
          </>
        }
        actions={<CustomToolbar />}
      >
        <Datagrid bulkActionButtons={false}>
          <FunctionField
            source="title"
            sortable={false}
            label={<span style={{ fontWeight: 'bold' }}>Project Name</span>}
            render={(record) => (
              <Link className="admin-link" to={`/projects/${record.id}/show`}>
                {record.title}
              </Link>
            )}
            sx={{
              marginTop: '20px',
            }}
          />
          <FunctionField
            source="projectCreator"
            sortable={false}
            label={<span style={{ fontWeight: 'bold' }}>Creator Name</span>}
            render={(record) => (
              <Link className="admin-link" to={`/users/${data.creatorId}/show`}>
                {record.projectCreator}
              </Link>
            )}
          />
          <TextField
            source="projectCreatorEmail"
            sortable={false}
            label={
              <span
                style={{
                  fontWeight: 'bold',
                  fontSize: '14px',
                  color: 'black',
                }}
              >
                Creator Email
              </span>
            }
          />
          <TextField
            source="regNo"
            sortable={false}
            label={
              <span
                style={{
                  fontWeight: 'bold',
                  fontSize: '14px',
                  color: 'black',
                }}
              >
                Registration No.
              </span>
            }
          />
          <TextField
            source="projectTags"
            sortable={false}
            label={
              <span
                style={{
                  fontWeight: 'bold',
                  fontSize: '14px',
                  color: 'black',
                }}
              >
                Project Tags
              </span>
            }
          />
          <TextField
            source="createdAt"
            sortable={false}
            label={
              <span
                style={{
                  fontWeight: 'bold',
                  fontSize: '14px',
                  color: 'black',
                }}
              >
                Created
              </span>
            }
          />
          <TextField
            source="updatedAt"
            sortable={false}
            label={
              <span
                style={{
                  fontWeight: 'bold',
                  fontSize: '14px',
                  color: 'black',
                }}
              >
                Updated
              </span>
            }
          />
          <FunctionField
            source="status"
            sortable={false}
            label={<span style={{ fontWeight: 'bold' }}>Status</span>}
            render={(record) => {
              const subsStatus = get(record, 'status');
              return (
                <Badge list={[{ label: subsStatus, className: 'admin-btn' }]} />
              );
            }}
          />
        </Datagrid>
      </List>
    </Box>
  );
};

export default OtherProjectList;
