import React from 'react';
import { SimpleShowLayout, TextField, FunctionField } from 'react-admin';
import { Typography } from '@mui/material';
import { rowStyle } from '../../helpers/helper';

const ExpandedTable = () => {
  return (
    <div className="mt-2 mx-3">
      <Typography variant="h6" color="text.primary" fontWeight={700}>
        Project Details
      </Typography>
      <SimpleShowLayout spacing={12} direction="row" sx={rowStyle}>
        <TextField
          source="registrationNo"
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Registration No.
            </span>
          }
        />
        <TextField
          source="created"
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Created
            </span>
          }
        />
        <TextField
          source="updated"
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Updated
            </span>
          }
        />
      </SimpleShowLayout>
      <SimpleShowLayout spacing={15} direction="row" sx={rowStyle}>
        <TextField
          source="creatorName"
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Project Creator
            </span>
          }
        />
        <TextField
          source="producer"
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Producer
            </span>
          }
        />
        <TextField
          source="director"
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Director
            </span>
          }
        />
        <TextField
          source="writer"
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Writer
            </span>
          }
        />
      </SimpleShowLayout>
      <SimpleShowLayout spacing={12} direction="row" sx={rowStyle}>
        <TextField
          source="genre"
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Genre
            </span>
          }
        />
        <TextField
          source="setting"
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Setting
            </span>
          }
        />
        <TextField
          source="format"
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Format
            </span>
          }
        />
      </SimpleShowLayout>
      <SimpleShowLayout spacing={10} direction="row" sx={rowStyle}>
        <TextField
          source="projectTags"
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Project Tags
            </span>
          }
        />
      </SimpleShowLayout>
      <SimpleShowLayout spacing={10} direction="row" sx={rowStyle}>
        <TextField
          source="totalFunding"
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Total Funding
            </span>
          }
        />
        <TextField
          source="totalBudget"
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Total Budget Value
            </span>
          }
          render={(record) =>
            record.showStatus === 'estimated'
              ? record.budget
              : record.showStatus === 'unestimated'
              ? `${record.unestimatedBudget} (estimated)`
              : record.budget
          }
        />

        <FunctionField
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Script
            </span>
          }
          source="script"
          render={(record) => {
            if (!record.script) return null;

            return (
              <a
                href={record.script}
                download
                className="admin-link"
                style={{ cursor: 'pointer' }}
              >
                Download
              </a>
            );
          }}
        />

        <FunctionField
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Treatment/Bible
            </span>
          }
          source="treatment"
          render={(record) => {
            if (!record.treatment) return null;

            return (
              <a
                href={record.treatment}
                download
                className="admin-link"
                style={{ cursor: 'pointer' }}
              >
                Download
              </a>
            );
          }}
        />
      </SimpleShowLayout>
      <SimpleShowLayout spacing={10} direction="row" sx={rowStyle}>
        <TextField
          source="status"
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Status
            </span>
          }
        />
      </SimpleShowLayout>
      <SimpleShowLayout spacing={10} direction="row" sx={rowStyle}>
        <TextField
          source="logLine"
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Log Line
            </span>
          }
        />
      </SimpleShowLayout>
      <SimpleShowLayout spacing={5} direction="row" sx={rowStyle}>
        <FunctionField
          label={
            <span
              style={{ fontWeight: 'bold', fontSize: '14px', color: 'black' }}
            >
              Snapshot
            </span>
          }
          source="projectSnapshot"
          render={(record) => {
            if (!record.projectSnapshot) return null;

            return (
              <span
                className="admin-link"
                role="button"
                onClick={() =>
                  window.open(
                    `${process.env.REACT_APP_WEBAPP_URL}/project/snap/${record.projectSnapshot}`,
                    '_blank'
                  )
                }
              >
                View most recent
              </span>
            );
          }}
        />
      </SimpleShowLayout>
    </div>
  );
};

export default ExpandedTable;
