import React from 'react';
import { get } from 'lodash';
import { Link } from 'react-router-dom';
import {
  List,
  TextField,
  TextInput,
  DateField,
  SelectColumnsButton,
  DatagridConfigurable,
  SearchInput,
  FunctionField,
  FilterButton,
  ExportButton,
  TopToolbar,
  AutocompleteInput,
  DateInput,
  EmailField,
} from 'react-admin';
import { getSubscriptionLabel } from '../../helpers/subscription';
import ExpandedTable from './projectTable';
import options from '../../configurations/option.json';
import Exporter from '../../sharedComponents/exporter';
import Badge from '../../sharedComponents/badge';

/**
 * A customized list component for displaying project data in the admin dashboard.
 *
 * @param {Object} props - Component properties.
 * @returns {JSX.Element} - React component.
 */
const ProjectList = (props) => {
  const statusList = get(options, 'Status', []);
  const formatList = get(options, 'Format', []);
  const genresList = get(options, 'generes', []);
  const settingList = get(options, 'Setting', []);
  const subscriptionTypeList = get(options, 'SubscriptionType', []);

  // Define filters for the list
  const projectFilter = [
    <SearchInput key="project-search" source="q" alwaysOn />,
    <TextInput key="projectName" label="Project Name" source="cover_title" />,
    <TextInput
      key="projectCreator"
      label="Creator Name"
      source="creator_username"
    />,
    <TextInput
      key="projectCreatorEmail"
      label="Creator Email"
      source="creator_email"
    />,
    <TextInput key="regNo" label="Registration No" source="regNo" />,
    <TextInput
      key="projectTags"
      label="Project Tags"
      source="basicInfo_tags_text"
    />,
    <TextInput key="producer" label="Producer" source="cover_producer" />,
    <TextInput key="director" label="Director" source="cover_director" />,
    <TextInput key="writer" label="Writer" source="cover_writer" />,
    <AutocompleteInput
      key="status"
      label="Status"
      source="basicInfo_status"
      choices={statusList.map((item, i) => ({ id: Number(i), item }))}
      optionText="item"
      optionValue="item"
    />,
    <AutocompleteInput
      key="subscriptionStatus"
      label="Subscription"
      source="project_subscription_type"
      choices={subscriptionTypeList.map((item, i) => ({ id: Number(i), item }))}
      optionText="item"
      optionValue="item"
    />,
    <AutocompleteInput
      key="format"
      label="Format"
      source="basicInfo_format"
      choices={formatList.map((item, i) => ({ id: Number(i), item }))}
      optionText="item"
      optionValue="item"
    />,
    <AutocompleteInput
      key="genre"
      label="Genre"
      source="basicInfo_genre"
      choices={genresList.map((item, i) => ({ id: Number(i), item }))}
      optionText="item"
      optionValue="item"
    />,
    <AutocompleteInput
      key="setting"
      label="Setting"
      source="basicInfo_setting"
      choices={settingList.map((item, i) => ({ id: Number(i), item }))}
      optionText="item"
      optionValue="item"
    />,
    <AutocompleteInput
      key="treatment"
      label="Treatment/Bible"
      source="treatment"
      choices={[
        { id: 'with', item: 'With' },
        { id: 'without', item: 'Without' },
      ]}
      optionText="item"
      optionValue="id"
    />,
    <AutocompleteInput
      key="script"
      label="Script"
      source="script"
      choices={[
        { id: 'with', item: 'With' },
        { id: 'without', item: 'Without' },
      ]}
      optionText="item"
      optionValue="id"
    />,
    <AutocompleteInput
      key="logLine"
      label="Log Line"
      source="basicInfo_logLine"
      choices={[
        { id: 'with', item: 'With' },
        { id: 'without', item: 'Without' },
      ]}
      optionText="item"
      optionValue="id"
    />,
    <AutocompleteInput
      key="snapshot"
      label="Snapshot"
      source="snapshot"
      choices={[
        { id: 'with', item: 'With' },
        { id: 'without', item: 'Without' },
      ]}
      optionText="item"
      optionValue="id"
    />,
    <DateInput
      key="createdAtBefore"
      label="Created Before"
      source="createdAtBefore"
    />,
    <DateInput
      key="createdAtAfter"
      label="Created After"
      source="createdAtAfter"
    />,
    <DateInput
      key="updatedAtBefore"
      label="Updated Before"
      source="updatedAtBefore"
    />,
    <DateInput
      key="updatedAtAfter"
      label="Updated After"
      source="updatedAtAfter"
    />,
  ];

  const PostListActions = () => (
    <TopToolbar>
      <SelectColumnsButton />
      <FilterButton />
      <ExportButton maxResults={1000000} />
    </TopToolbar>
  );

  // Render the project list component
  return (
    <List
      {...props}
      actions={<PostListActions />}
      filters={projectFilter}
      exporter={(list) => Exporter(list, 'projects')}
    >
      <DatagridConfigurable
        preferenceKey="projects.datagrid"
        expand={<ExpandedTable />}
        bulkActionButtons={false}
        omit={[
          'producer',
          'director',
          'writer',
          'format',
          'genre',
          'setting',
          'budget',
          'financePlan',
          'treatment',
          'script',
          'logLine',
          'lastSnapshotHash',
        ]}
      >
        <FunctionField
          source="projectName"
          label={<span style={{ fontWeight: 'bold' }}>Project Name</span>}
          render={(record) => (
            <Link className="admin-link" to={`/projects/${record.id}/show`}>
              {record.projectName}
            </Link>
          )}
        />
        <FunctionField
          source="creatorName"
          label={<span style={{ fontWeight: 'bold' }}>Creator Name</span>}
          render={(record) => (
            <Link
              className="admin-link"
              to={`/users/${record.projectCreatorId}/show`}
            >
              {record.creatorName}
            </Link>
          )}
        />
        <EmailField
          source="creatorEmail"
          label={<span style={{ fontWeight: 'bold' }}>Creator Email</span>}
        />
        <TextField
          source="registrationNo"
          label={<span style={{ fontWeight: 'bold' }}>Registration No.</span>}
        />
        <TextField
          source="projectTags"
          sortable={false}
          label={<span style={{ fontWeight: 'bold' }}>Project Tags</span>}
        />
        <DateField
          source="created"
          label={<span style={{ fontWeight: 'bold' }}>Created</span>}
          showTime={true}
        />
        <DateField
          source="updated"
          label={<span style={{ fontWeight: 'bold' }}>Updated</span>}
          showTime={true}
        />
        <TextField
          source="status"
          label={<span style={{ fontWeight: 'bold' }}>Status</span>}
        />
        <TextField
          source="producer"
          label={<span style={{ fontWeight: 'bold' }}>Producer</span>}
        />
        <TextField
          source="director"
          label={<span style={{ fontWeight: 'bold' }}>Director</span>}
        />
        <TextField
          source="writer"
          label={<span style={{ fontWeight: 'bold' }}>Writer</span>}
        />
        <TextField
          sortable={false}
          source="format"
          label={<span style={{ fontWeight: 'bold' }}>Format</span>}
        />
        <TextField
          sortable={false}
          source="genre"
          label={<span style={{ fontWeight: 'bold' }}>Genre</span>}
        />
        <TextField
          sortable={false}
          source="setting"
          label={<span style={{ fontWeight: 'bold' }}>Setting</span>}
        />
        <FunctionField
          source="totalBudget"
          label={<span style={{ fontWeight: 'bold' }}>Total Budget</span>}
          render={(record) => (
            <>
              {record.showStatus === 'estimated'
                ? record.totalBudget
                : record.showStatus === 'unestimated'
                ? `${record.unestimatedBudget} (estimated)`
                : record.totalBudget}
            </>
          )}
        />
        <TextField
          source="totalFunding"
          label={<span style={{ fontWeight: 'bold' }}>Total Funding</span>}
        />
        <FunctionField
          source="subscription"
          sortable={false}
          label={<span style={{ fontWeight: 'bold' }}>Subscription</span>}
          render={(record) => {
            const subsStatus = get(record, 'subscription');
            const { badgeColor, labelText } = getSubscriptionLabel(subsStatus);
            return (
              <Badge
                list={[
                  {
                    label: labelText,
                    className: `badge-btn ${badgeColor} fs-12`,
                  },
                ]}
              />
            );
          }}
        />
        <FunctionField
          sortable={false}
          label={<span style={{ fontWeight: 'bold' }}> Treatment/Bible</span>}
          source="treatment"
          render={(record) =>
            record.treatment ? (
              <a className="admin-link" href={record.treatment} download>
                Download
              </a>
            ) : null
          }
        />
        <FunctionField
          sortable={false}
          label={<span style={{ fontWeight: 'bold' }}> Script</span>}
          source="script"
          render={(record) =>
            record.script ? (
              <a className="admin-link" href={record.script} download>
                Download
              </a>
            ) : null
          }
        />
        <TextField
          sortable={false}
          source="logLine"
          label={<span style={{ fontWeight: 'bold' }}>Log Lines</span>}
        />
        <FunctionField
          sortable={false}
          label={<span style={{ fontWeight: 'bold' }}> Project Snapshot</span>}
          source="projectSnapshot "
          render={(record) =>
            record?.projectSnapshot ? (
              <span
                className="admin-link"
                role="button"
                onClick={() =>
                  window.open(
                    `${process.env.REACT_APP_WEBAPP_URL}/project/snap/${record.projectSnapshot}`,
                    '_blank'
                  )
                }
              >
                View most recent
              </span>
            ) : null
          }
        />
      </DatagridConfigurable>
    </List>
  );
};
export default ProjectList;
