import { useState } from 'react';
import {
  useNotify,
  useRedirect,
  useGetRecordId,
  useDataProvider,
  useGetOne,
} from 'react-admin';
import { <PERSON><PERSON>, Card, CardContent, Typography } from '@mui/material';
import { get } from 'lodash';
import LoaderWithCallout from '../../sharedComponents/LoaderWithCallout';

const CallOutCreate = () => {
  const [formData, setFormData] = useState({ content: '' });
  const [loading, setLoading] = useState(false);
  const recordId = useGetRecordId();
  const { data } = useGetOne('callouts', { id: recordId });
  // const [recordId, setRecordId] = useState(null);
  const notify = useNotify();
  const redirect = useRedirect();
  const dataProvider = useDataProvider();

  // useEffect(() => {
  //   const hashPath = window.location.hash; // e.g., #/callouts/5f4917994704d900064255e2/create
  //   const match = hashPath.match(/#\/callouts\/([^/]+)\/create/);
  //   if (match) {
  //     setRecordId(match[1]);
  //   }
  // }, []);
  const handleGenerateCallout = () => {
    if (!formData.content.trim()) {
      notify('Please enter some notes before generating.', { type: 'warning' });
      return;
    }

    setLoading(true);

    if (!recordId) {
      console.error('Error: recordId is missing');
      notify('Error: Unable to determine record ID.', { type: 'error' });
      return;
    }

    const savedData = {
      id: recordId,
      inputs: [
        {
          name: 'callOut',
          type: 'longText',
          data: formData.content,
        },
      ],
    };

    dataProvider
      .create('execute', { data: savedData })
      .then(async (resp) => {
        const content = get(resp, 'data.data.result.data', '');
        const header = get(resp, 'data.data.header', '');
        const body = get(data, 'body', {});
        const updateData = {
          name: 'Generated Callout',
          isPublished: get(resp, 'isPublished', false),
          body: {
            ...body,
            title: header,
            content: content,
          },
          prompt: get(formData, 'content', ''),
        };
        const updatedData = await dataProvider.update(`v1/callout`,  {
          id: recordId,
          data: updateData,
          previousData: body,
        },);
        return updatedData;
      })
      .then((resp) => {
        notify("Callout's profile updated and published successfully", {
          type: 'success',
          multiLine: true,
          autoHideDuration: 2500,
        });
        redirect(`/callouts/${get(resp, 'data.id', '')}`);
      })
      .catch((error) => {
        notify(`${String(error)}`, {
          type: 'error',
          multiLine: true,
          autoHideDuration: 2500,
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <Card sx={{ mt: 3 }}>
      {loading ? (
        <div className="pt-3">
          <LoaderWithCallout
            // title="Edit call out"
            calloutText="Please wait while the call out is generated"
          />
        </div>
      ) : (
        <>
          <CardContent>
            <Typography variant="h6">Call out details</Typography>
            <Typography variant="subtitle1">
              Notes for call out generation
            </Typography>
            <textarea
              rows={18}
              placeholder="This profile will display in the call out"
              style={{ width: '100%', padding: '8px', fontSize: '16px' }}
              onChange={(e) => setFormData({ content: e.target.value })}
            />
          </CardContent>
          <CardContent
            sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, my: 0 }}
          >
            <Button
              variant="outlined"
              onClick={() => redirect(`/callouts/${recordId}`)}
            >
              Switch to editor view
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleGenerateCallout}
              disabled={loading}
            >
              {loading ? 'Generating...' : 'Generate Call Out'}
            </Button>
          </CardContent>
        </>
      )}
    </Card>
  );
};

export default CallOutCreate;
