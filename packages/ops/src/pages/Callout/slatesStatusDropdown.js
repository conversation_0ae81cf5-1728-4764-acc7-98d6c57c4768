import { useState } from 'react';
import { Select, MenuItem, FormControl, Chip } from '@mui/material';
// import { useNotify, useDataProvider, useRefresh } from 'react-admin';
// ['awaiting_feedback', 'lets_talk', 'not_interested', 'tracking'],
const statusOptions = [
  {
    label: 'Awaiting feedback',
    value: 'awaiting_feedback',
    color: '#EAEAEA',
    textColor: '#000',
  },
  {
    label: 'Let’s talk',
    value: 'lets_talk',
    color: '#3DDC97',
    textColor: '#000',
  },
  { label: 'Tracking', value: 'tracking', color: '#FFD700', textColor: '#000' },
  {
    label: 'Not for me',
    value: 'not_interested',
    color: '#E53935',
    textColor: '#fff',
  },
];

const StatusField = ({ record, onStatusChange }) => {
  const [status, setStatus] = useState(record?.status || 'awaiting_feedback');
  // const notify = useNotify();
  // const dataProvider = useDataProvider();
  // const refresh = useRefresh(); // To refresh the UI

  const handleChange = async (event) => {
    const newStatus = event.target.value;
    setStatus(newStatus);

    // try {
    // await dataProvider.update('projects', {
    //   id: record.id,
    //   data: { status: newStatus },
    // });

    // notify('Status updated successfully', { type: 'success' });

    // **Perform additional operations here**
    if (onStatusChange) {
      onStatusChange(newStatus, record);
    }

    //   // Refresh the UI if needed
    //   refresh();
    // } catch (error) {
    //   notify('Error updating status', { type: 'warning' });
    // }
  };

  return (
    <FormControl variant="outlined" size="small">
      <Select
        value={status}
        onChange={handleChange}
        sx={{
          backgroundColor: statusOptions.find((opt) => opt.value === status)
            ?.color,
          color: statusOptions.find((opt) => opt.value === status)?.textColor,
          fontWeight: 'bold',
          borderRadius: '20px',
          minWidth: 150,
        }}
      >
        {statusOptions.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            <Chip
              label={option.label}
              sx={{
                backgroundColor: option.color,
                color: option.textColor,
                fontWeight: 'bold',
              }}
            />
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default StatusField;
