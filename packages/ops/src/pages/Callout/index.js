import {
  List,
  TextField,
  ChipField,
  FunctionField,
  TopToolbar,
  SelectColumnsButton,
  FilterButton,
  ExportButton,
  SearchInput,
  TextInput,
  AutocompleteInput,
  DeleteWithConfirmButton,
  Link,
  useRedirect,
  CreateButton,
  DatagridConfigurable,
} from 'react-admin';
import { get } from 'lodash';
import { Box, IconButton, Typography } from '@mui/material';
import { Edit, Visibility, Delete } from '@mui/icons-material';
import InboxIcon from '@mui/icons-material/Inbox';
import Badge from '../../sharedComponents/badge';

import {
  getStatusBackgroundColor,
  getStatusTextColor,
} from '../../helpers/helper';

const Callouts = (props) => {
  const redirect = useRedirect();

  const handleView = (record) => {
    window.open(
      `${process.env.REACT_APP_WEBAPP_URL}/callouts/view/${record.id}`,
      '_blank'
    );
  };

  const calloutFilter = [
    <SearchInput key="callout-search" source="q" alwaysOn />,
    <TextInput key="name" label="Callout Name" source="name" />,
    <TextInput
      key="discovererName"
      label="Discoverer Name"
      source="discoverer_name"
    />,
    <AutocompleteInput
      key="isPublished"
      label="Status"
      source="isPublished"
      choices={[
        { key: true, label: 'Published' },
        { key: false, label: 'Unpublished' },
      ]}
      optionText="label"
      optionValue="key"
    />,
  ];
  const CallOutListActions = () => (
    <TopToolbar>
      <SelectColumnsButton />
      <FilterButton />
      <ExportButton maxResults={1000000} />
      <CreateButton basePath="/callouts" />
    </TopToolbar>
  );
  return (
    <>
      <List {...props} actions={<CallOutListActions />} filters={calloutFilter}>
        <DatagridConfigurable
          bulkActionButtons={false}
          preferenceKey="callouts.datagrid"
          empty={
            <>
              <Box textAlign="center" p={2}>
                <InboxIcon sx={{ fontSize: 100 }} />
                <Typography variant="h6" color="textSecondary">
                  No Submissions Yet
                </Typography>
              </Box>
            </>
          }
        >
          <FunctionField
            source="name"
            label={<span style={{ fontWeight: 'bold' }}>Call Out Name</span>}
            render={(record) => (
              <Link className="admin-link" to={`/callouts/${record.id}/show`}>
                {record.name}
              </Link>
            )}
          />
          <TextField
            source="companyName"
            label={<span style={{ fontWeight: 'bold' }}>Organisation</span>}
          />
          <FunctionField
            source="discoverer"
            label={<span style={{ fontWeight: 'bold' }}>Discoverer</span>}
            render={(record) => (
              <Link
                className="admin-link"
                to={`/users/${record.discovererId}/show`}
              >
                {record.discoverer}
              </Link>
            )}
          />
          <TextField
            source="createdAt"
            label={<span style={{ fontWeight: 'bold' }}>Created At</span>}
          />
          <TextField
            source="totalSubmissions"
            label={
              <span style={{ fontWeight: 'bold' }}>Total Submissions</span>
            }
          />
          <TextField
            source="newsubmissionsCount"
            label={<span style={{ fontWeight: 'bold' }}>New Submissions</span>}
          />
          <TextField
            source="totalSlates"
            label={<span style={{ fontWeight: 'bold' }}>Slate</span>}
          />

          <FunctionField
            source="isPublished"
            label={<span style={{ fontWeight: 'bold' }}>Status</span>}
            render={(record) => {
              const isPublished = get(record, 'isPublished');
              const badgeColor = isPublished ? 'badge-success' : 'badge-danger';
              const labelText = isPublished ? 'Public' : 'Private';
              return (
                <Badge
                  list={[
                    {
                      label: labelText,
                      className: `badge-btn ${badgeColor} fs-12`,
                    },
                  ]}
                />
              );
            }}
          />
          <FunctionField
            label={<span style={{ fontWeight: 'bold' }}>Subscription</span>}
            render={(record) =>
              record ? (
                <ChipField
                  source="subscription"
                  record={record}
                  style={{
                    backgroundColor: getStatusBackgroundColor(
                      record.subscription
                    ),
                    textTransform: 'capitalize',
                    color: getStatusTextColor(record.subscription),
                    fontWeight: '700',
                  }}
                />
              ) : null
            }
          />
          <FunctionField
            label={<span style={{ fontWeight: 'bold' }}>Actions</span>}
            render={(record) => (
              <div style={{ display: 'flex' }}>
                <IconButton
                  onClick={() => {
                    redirect(`/callouts/${record.id}`);
                  }}
                >
                  <Edit style={{ color: '#0D0D3F' }} />
                </IconButton>
                <IconButton onClick={() => handleView(record)}>
                  <Visibility style={{ color: '#0D0D3F' }} />
                </IconButton>
                <DeleteWithConfirmButton
                  confirmTitle={`Delete Callout: ${record.name}`}
                  confirmContent="You will not be able to recover this record. Are you sure?"
                  confirmColor="warning"
                  className="opsActions"
                  label=""
                  icon={<Delete style={{ color: '#0D0D3F' }} />}
                />
              </div>
            )}
          />
        </DatagridConfigurable>
      </List>
    </>
  );
};

export default Callouts;
