{"name": "smash-cupid-panel", "version": "0.1.0", "private": true, "dependencies": {"@ckeditor/ckeditor5-build-multi-root": "^41.3.1", "@mui/material": "^6.4.6", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.9", "bootstrap": "^5.3.3", "classnames": "^2.5.1", "css-loader": "^7.1.2", "jwt-decode": "^4.0.0", "moment": "^2.30.1", "query-string": "^9.1.1", "ra-data-json-server": "^5.5.4", "react": "^19.0.0", "react-admin": "^5.5.4", "react-bootstrap": "^2.10.9", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-inlinesvg": "^4.2.0", "react-load-script": "^0.0.6", "react-redux": "^9.2.0", "react-router-dom": "^7.2.0", "react-scripts": "5.0.1", "sass": "^1.85.0", "validator": "^13.12.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}